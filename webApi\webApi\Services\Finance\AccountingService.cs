using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// خدمة المحاسبة الأساسية
    /// Accounting Service Implementation
    /// </summary>
    public class AccountingService : IAccountingService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<AccountingService> _logger;

        public AccountingService(TasksDbContext context, ILogger<AccountingService> logger)
        {
            _context = context;
            _logger = logger;
        }

        // ===================================================================
        // إدارة الحسابات (Chart of Accounts)
        // ===================================================================

        public async Task<Account> CreateAccountAsync(CreateAccountRequest request)
        {
            try
            {
                // التحقق من عدم تكرار رمز الحساب
                var existingAccount = await _context.Set<Account>()
                    .FirstOrDefaultAsync(a => a.AccountCode == request.AccountCode);

                if (existingAccount != null)
                {
                    throw new InvalidOperationException($"رمز الحساب {request.AccountCode} موجود مسبقاً");
                }

                // تحديد مستوى الحساب
                int accountLevel = 1;
                if (request.ParentAccountId.HasValue)
                {
                    var parentAccount = await _context.Set<Account>()
                        .FindAsync(request.ParentAccountId.Value);
                    if (parentAccount != null)
                    {
                        accountLevel = parentAccount.AccountLevel + 1;
                    }
                }

                var account = new Account
                {
                    AccountCode = request.AccountCode,
                    AccountName = request.AccountName,
                    AccountType = request.AccountType,
                    ParentAccountId = request.ParentAccountId,
                    AccountLevel = accountLevel,
                    BalanceType = request.BalanceType,
                    LinkedPersonId = request.LinkedPersonId,
                    Description = request.Description,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<Account>().Add(account);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء حساب جديد: {AccountCode} - {AccountName}", 
                    account.AccountCode, account.AccountName);

                return account;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الحساب: {AccountCode}", request.AccountCode);
                throw;
            }
        }

        public async Task<List<Account>> GetAllAccountsAsync()
        {
            try
            {
                return await _context.Set<Account>()
                    .Include(a => a.ParentAccount)
                    .Include(a => a.LinkedPerson)
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.AccountCode)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحسابات");
                throw;
            }
        }

        public async Task<List<Account>> GetAccountsHierarchyAsync()
        {
            try
            {
                var accounts = await _context.Set<Account>()
                    .Include(a => a.ChildAccounts)
                    .Include(a => a.LinkedPerson)
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.AccountCode)
                    .ToListAsync();

                return accounts.Where(a => a.ParentAccountId == null).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الهيكل الهرمي للحسابات");
                throw;
            }
        }

        public async Task<Account?> GetAccountByIdAsync(int accountId)
        {
            try
            {
                return await _context.Set<Account>()
                    .Include(a => a.ParentAccount)
                    .Include(a => a.LinkedPerson)
                    .FirstOrDefaultAsync(a => a.Id == accountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحساب: {AccountId}", accountId);
                throw;
            }
        }

        public async Task<decimal> GetAccountBalanceAsync(int accountId, DateTime? asOfDate = null)
        {
            try
            {
                var account = await GetAccountByIdAsync(accountId);
                if (account == null)
                {
                    throw new ArgumentException($"الحساب غير موجود: {accountId}");
                }

                var query = _context.Set<JournalEntryLine>()
                    .Include(jel => jel.JournalEntry)
                    .Where(jel => jel.AccountId == accountId && 
                                 jel.JournalEntry.Status == "posted");

                if (asOfDate.HasValue)
                {
                    query = query.Where(jel => jel.JournalEntry.EntryDate <= asOfDate.Value);
                }

                var lines = await query.ToListAsync();

                decimal balance = 0;
                foreach (var line in lines)
                {
                    if (account.BalanceType == "Debit")
                    {
                        balance += line.DebitAmount - line.CreditAmount;
                    }
                    else
                    {
                        balance += line.CreditAmount - line.DebitAmount;
                    }
                }

                return balance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب رصيد الحساب: {AccountId}", accountId);
                throw;
            }
        }

        public async Task<bool> UpdateAccountBalanceAsync(int accountId, decimal amount, bool isDebit)
        {
            try
            {
                var account = await _context.Set<Account>().FindAsync(accountId);
                if (account == null)
                {
                    return false;
                }

                if (account.BalanceType == "Debit")
                {
                    account.CurrentBalance += isDebit ? amount : -amount;
                }
                else
                {
                    account.CurrentBalance += isDebit ? -amount : amount;
                }

                account.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث رصيد الحساب: {AccountId}", accountId);
                throw;
            }
        }

        // ===================================================================
        // إدارة القيود اليومية (Journal Entries)
        // ===================================================================

        public async Task<JournalEntry> CreateJournalEntryAsync(CreateJournalEntryRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // توليد رقم القيد
                var entryNumber = await GenerateEntryNumberAsync();

                var journalEntry = new JournalEntry
                {
                    EntryNumber = entryNumber,
                    EntryDate = request.EntryDate,
                    Description = request.Description,
                    ReferenceType = request.ReferenceType,
                    ReferenceId = request.ReferenceId,
                    ReferenceNumber = request.ReferenceNumber,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<JournalEntry>().Add(journalEntry);
                await _context.SaveChangesAsync();

                // إضافة خطوط القيد
                decimal totalDebit = 0, totalCredit = 0;
                int lineOrder = 1;

                foreach (var lineRequest in request.Lines)
                {
                    var line = new JournalEntryLine
                    {
                        JournalEntryId = journalEntry.Id,
                        AccountId = lineRequest.AccountId,
                        DebitAmount = lineRequest.DebitAmount,
                        CreditAmount = lineRequest.CreditAmount,
                        Description = lineRequest.Description,
                        CostCenterId = lineRequest.CostCenterId,
                        TaskId = lineRequest.ProjectId, // Using TaskId instead of ProjectId
                        LineOrder = lineOrder++,
                        CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                    };

                    _context.Set<JournalEntryLine>().Add(line);
                    totalDebit += lineRequest.DebitAmount;
                    totalCredit += lineRequest.CreditAmount;
                }

                // تحديث إجماليات القيد
                journalEntry.TotalDebit = totalDebit;
                journalEntry.TotalCredit = totalCredit;

                await _context.SaveChangesAsync();

                // التحقق من التوازن
                if (!await ValidateJournalEntryBalanceAsync(journalEntry))
                {
                    throw new InvalidOperationException("القيد غير متوازن");
                }

                await transaction.CommitAsync();

                _logger.LogInformation("تم إنشاء قيد يومي جديد: {EntryNumber}", entryNumber);
                return journalEntry;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في إنشاء القيد اليومي");
                throw;
            }
        }

        public async Task<bool> PostJournalEntryAsync(int journalEntryId, int postedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var journalEntry = await _context.Set<JournalEntry>()
                    .Include(je => je.Lines)
                    .ThenInclude(jel => jel.Account)
                    .FirstOrDefaultAsync(je => je.Id == journalEntryId);

                if (journalEntry == null || journalEntry.Status != "draft")
                {
                    return false;
                }

                // التحقق من التوازن مرة أخرى
                if (!await ValidateJournalEntryBalanceAsync(journalEntry))
                {
                    throw new InvalidOperationException("القيد غير متوازن");
                }

                // تحديث أرصدة الحسابات
                foreach (var line in journalEntry.Lines)
                {
                    await UpdateAccountBalanceAsync(line.AccountId, 
                        line.DebitAmount > 0 ? line.DebitAmount : line.CreditAmount,
                        line.DebitAmount > 0);
                }

                // تحديث حالة القيد
                journalEntry.Status = "posted";
                journalEntry.PostedBy = postedBy;
                journalEntry.PostedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("تم ترحيل القيد: {EntryNumber}", journalEntry.EntryNumber);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في ترحيل القيد: {JournalEntryId}", journalEntryId);
                throw;
            }
        }

        public async Task<bool> ValidateJournalEntryBalanceAsync(JournalEntry entry)
        {
            try
            {
                return Math.Abs(entry.TotalDebit - entry.TotalCredit) < 0.01m;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من توازن القيد: {EntryId}", entry.Id);
                return false;
            }
        }

        public async Task<bool> CancelJournalEntryAsync(int journalEntryId, int cancelledBy)
        {
            try
            {
                var journalEntry = await _context.Set<JournalEntry>()
                    .FindAsync(journalEntryId);

                if (journalEntry == null || journalEntry.Status == "cancelled")
                {
                    return false;
                }

                journalEntry.Status = "cancelled";
                journalEntry.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إلغاء القيد: {EntryNumber}", journalEntry.EntryNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إلغاء القيد: {JournalEntryId}", journalEntryId);
                throw;
            }
        }

        public async Task<List<JournalEntry>> GetJournalEntriesAsync(JournalEntryFilter filter)
        {
            try
            {
                var query = _context.Set<JournalEntry>()
                    .Include(je => je.Lines)
                    .ThenInclude(jel => jel.Account)
                    .Include(je => je.Creator)
                    .AsQueryable();

                if (filter.FromDate.HasValue)
                    query = query.Where(je => je.EntryDate >= filter.FromDate.Value);

                if (filter.ToDate.HasValue)
                    query = query.Where(je => je.EntryDate <= filter.ToDate.Value);

                if (!string.IsNullOrEmpty(filter.Status))
                    query = query.Where(je => je.Status == filter.Status);

                if (!string.IsNullOrEmpty(filter.ReferenceType))
                    query = query.Where(je => je.ReferenceType == filter.ReferenceType);

                if (filter.AccountId.HasValue)
                    query = query.Where(je => je.Lines.Any(l => l.AccountId == filter.AccountId.Value));

                return await query
                    .OrderByDescending(je => je.EntryDate)
                    .Skip((filter.PageNumber - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على القيود اليومية");
                throw;
            }
        }

        // ===================================================================
        // التقارير المحاسبية
        // ===================================================================

        public async Task<TrialBalanceReport> GenerateTrialBalanceAsync(DateTime asOfDate)
        {
            try
            {
                var accounts = await _context.Set<Account>()
                    .Where(a => a.IsActive)
                    .ToListAsync();

                var report = new TrialBalanceReport
                {
                    AsOfDate = asOfDate,
                    Items = new List<TrialBalanceItem>()
                };

                foreach (var account in accounts)
                {
                    var balance = await GetAccountBalanceAsync(account.Id, asOfDate);
                    
                    var item = new TrialBalanceItem
                    {
                        AccountId = account.Id,
                        AccountCode = account.AccountCode,
                        AccountName = account.AccountName
                    };

                    if (account.BalanceType == "Debit")
                    {
                        item.DebitBalance = balance > 0 ? balance : 0;
                        item.CreditBalance = balance < 0 ? Math.Abs(balance) : 0;
                    }
                    else
                    {
                        item.CreditBalance = balance > 0 ? balance : 0;
                        item.DebitBalance = balance < 0 ? Math.Abs(balance) : 0;
                    }

                    report.Items.Add(item);
                }

                report.TotalDebits = report.Items.Sum(i => i.DebitBalance);
                report.TotalCredits = report.Items.Sum(i => i.CreditBalance);

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير ميزان المراجعة");
                throw;
            }
        }

        public async Task<AccountStatementReport> GenerateAccountStatementAsync(int accountId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var account = await GetAccountByIdAsync(accountId);
                if (account == null)
                {
                    throw new ArgumentException($"الحساب غير موجود: {accountId}");
                }

                var openingBalance = await GetAccountBalanceAsync(accountId, fromDate.AddDays(-1));

                var lines = await _context.Set<JournalEntryLine>()
                    .Include(jel => jel.JournalEntry)
                    .Where(jel => jel.AccountId == accountId &&
                                 jel.JournalEntry.Status == "posted" &&
                                 jel.JournalEntry.EntryDate >= fromDate &&
                                 jel.JournalEntry.EntryDate <= toDate)
                    .OrderBy(jel => jel.JournalEntry.EntryDate)
                    .ToListAsync();

                var report = new AccountStatementReport
                {
                    Account = account,
                    FromDate = fromDate,
                    ToDate = toDate,
                    OpeningBalance = openingBalance,
                    Transactions = new List<AccountStatementItem>()
                };

                decimal runningBalance = openingBalance;

                foreach (var line in lines)
                {
                    if (account.BalanceType == "Debit")
                    {
                        runningBalance += line.DebitAmount - line.CreditAmount;
                    }
                    else
                    {
                        runningBalance += line.CreditAmount - line.DebitAmount;
                    }

                    var item = new AccountStatementItem
                    {
                        Date = line.JournalEntry.EntryDate,
                        Description = line.Description ?? line.JournalEntry.Description ?? "",
                        ReferenceNumber = line.JournalEntry.ReferenceNumber ?? line.JournalEntry.EntryNumber,
                        DebitAmount = line.DebitAmount,
                        CreditAmount = line.CreditAmount,
                        Balance = runningBalance
                    };

                    report.Transactions.Add(item);
                }

                report.ClosingBalance = runningBalance;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج كشف الحساب: {AccountId}", accountId);
                throw;
            }
        }

        public async Task<List<AccountBalance>> GetAccountBalancesAsync(DateTime asOfDate)
        {
            try
            {
                var accounts = await _context.Set<Account>()
                    .Where(a => a.IsActive)
                    .ToListAsync();

                var balances = new List<AccountBalance>();

                foreach (var account in accounts)
                {
                    var balance = await GetAccountBalanceAsync(account.Id, asOfDate);
                    
                    balances.Add(new AccountBalance
                    {
                        AccountId = account.Id,
                        AccountCode = account.AccountCode,
                        AccountName = account.AccountName,
                        Balance = balance,
                        AsOfDate = asOfDate
                    });
                }

                return balances.OrderBy(b => b.AccountCode).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أرصدة الحسابات");
                throw;
            }
        }

        // ===================================================================
        // دوال مساعدة
        // ===================================================================

        private async Task<string> GenerateEntryNumberAsync()
        {
            var year = DateTime.Now.Year;
            var prefix = $"JE{year}";

            var lastEntry = await _context.Set<JournalEntry>()
                .Where(je => je.EntryNumber.StartsWith(prefix))
                .OrderByDescending(je => je.EntryNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastEntry != null)
            {
                var lastNumberStr = lastEntry.EntryNumber.Substring(prefix.Length);
                if (int.TryParse(lastNumberStr, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D6}";
        }
    }
}
