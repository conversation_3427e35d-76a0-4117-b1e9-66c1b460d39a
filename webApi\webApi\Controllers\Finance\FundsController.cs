using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services.Finance;
using webApi.Models.Finance;
using webApi.Attributes;

namespace webApi.Controllers.Finance
{
    /// <summary>
    /// متحكم إدارة الصناديق
    /// Funds Controller
    /// </summary>
    [Route("api/finance/[controller]")]
    [ApiController]
    [Authorize]
    [Produces("application/json")]
    public class FundsController : ControllerBase
    {
        private readonly IFundManagementService _fundManagementService;
        private readonly ILogger<FundsController> _logger;

        public FundsController(IFundManagementService fundManagementService, ILogger<FundsController> logger)
        {
            _fundManagementService = fundManagementService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الصناديق
        /// </summary>
        [HttpGet]
        [HasPermission("finance.funds.view")]
        public async Task<ActionResult<List<Fund>>> GetAllFunds()
        {
            try
            {
                var funds = await _fundManagementService.GetAllFundsAsync();
                return Ok(funds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الصناديق");
                return StatusCode(500, new { message = "خطأ في الحصول على الصناديق" });
            }
        }

        /// <summary>
        /// الحصول على صندوق بالمعرف
        /// </summary>
        [HttpGet("{id}")]
        [HasPermission("finance.funds.view")]
        public async Task<ActionResult<Fund>> GetFundById(int id)
        {
            try
            {
                var fund = await _fundManagementService.GetFundByIdAsync(id);
                if (fund == null)
                {
                    return NotFound(new { message = "الصندوق غير موجود" });
                }

                return Ok(fund);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الصندوق: {FundId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على الصندوق" });
            }
        }

        /// <summary>
        /// الحصول على رصيد الصندوق
        /// </summary>
        [HttpGet("{id}/balance")]
        [HasPermission("finance.funds.view")]
        public async Task<ActionResult<decimal>> GetFundBalance(int id)
        {
            try
            {
                var balance = await _fundManagementService.GetFundBalanceAsync(id);
                return Ok(new { fundId = id, balance });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رصيد الصندوق: {FundId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على رصيد الصندوق" });
            }
        }

        /// <summary>
        /// إنشاء صندوق جديد
        /// </summary>
        [HttpPost]
        [HasPermission("finance.funds.create")]
        public async Task<ActionResult<Fund>> CreateFund([FromBody] CreateFundRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.CreatedBy = GetCurrentUserId();
                var fund = await _fundManagementService.CreateFundAsync(request);
                return CreatedAtAction(nameof(GetFundById), new { id = fund.Id }, fund);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الصندوق: {FundCode}", request.FundCode);
                return StatusCode(500, new { message = "خطأ في إنشاء الصندوق" });
            }
        }

        /// <summary>
        /// الحصول على حركات الصندوق
        /// </summary>
        [HttpGet("{id}/transactions")]
        [HasPermission("finance.funds.view")]
        public async Task<ActionResult<List<FundTransaction>>> GetFundTransactions(
            int id, 
            [FromQuery] FundTransactionFilter filter)
        {
            try
            {
                var transactions = await _fundManagementService.GetFundTransactionsAsync(id, filter);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على حركات الصندوق: {FundId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على حركات الصندوق" });
            }
        }

        /// <summary>
        /// إضافة حركة للصندوق
        /// </summary>
        [HttpPost("{id}/transactions")]
        [HasPermission("finance.funds.manage")]
        public async Task<ActionResult<FundTransaction>> AddFundTransaction(
            int id, 
            [FromBody] CreateFundTransactionRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.FundId = id;
                request.CreatedBy = GetCurrentUserId();
                
                var transaction = await _fundManagementService.AddFundTransactionAsync(request);
                return Ok(transaction);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة حركة للصندوق: {FundId}", id);
                return StatusCode(500, new { message = "خطأ في إضافة حركة للصندوق" });
            }
        }

        /// <summary>
        /// تحويل بين الصناديق
        /// </summary>
        [HttpPost("transfer")]
        [HasPermission("finance.funds.transfer")]
        public async Task<ActionResult<FundTransferResult>> TransferBetweenFunds([FromBody] FundTransferRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.CreatedBy = GetCurrentUserId();
                var result = await _fundManagementService.TransferBetweenFundsAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(new { message = result.Message });
                }
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحويل الأموال بين الصناديق");
                return StatusCode(500, new { message = "خطأ في تحويل الأموال بين الصناديق" });
            }
        }

        /// <summary>
        /// تحويل من صندوق إلى حساب شخصي
        /// </summary>
        [HttpPost("funding-transfer")]
        [HasPermission("finance.funds.funding_transfer")]
        public async Task<ActionResult<FundingTransfer>> CreateFundingTransfer([FromBody] FundingTransferRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.CreatedBy = GetCurrentUserId();
                var transfer = await _fundManagementService.TransferToPersonalAccountAsync(request);
                return Ok(transfer);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التحويل التمويلي");
                return StatusCode(500, new { message = "خطأ في إنشاء التحويل التمويلي" });
            }
        }

        /// <summary>
        /// الحصول على التحويلات التمويلية
        /// </summary>
        [HttpGet("funding-transfers")]
        [HasPermission("finance.funds.view")]
        public async Task<ActionResult<List<FundingTransfer>>> GetFundingTransfers([FromQuery] FundingTransferFilter filter)
        {
            try
            {
                var transfers = await _fundManagementService.GetFundingTransfersAsync(filter);
                return Ok(transfers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على التحويلات التمويلية");
                return StatusCode(500, new { message = "خطأ في الحصول على التحويلات التمويلية" });
            }
        }

        /// <summary>
        /// اعتماد تحويل تمويلي
        /// </summary>
        [HttpPost("funding-transfers/{id}/approve")]
        [HasPermission("finance.funds.approve_transfer")]
        public async Task<ActionResult> ApproveFundingTransfer(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _fundManagementService.ApproveFundingTransferAsync(id, userId);

                if (success)
                {
                    return Ok(new { message = "تم اعتماد التحويل بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "لا يمكن اعتماد التحويل" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد التحويل التمويلي: {TransferId}", id);
                return StatusCode(500, new { message = "خطأ في اعتماد التحويل التمويلي" });
            }
        }

        /// <summary>
        /// إكمال تحويل تمويلي
        /// </summary>
        [HttpPost("funding-transfers/{id}/complete")]
        [HasPermission("finance.funds.complete_transfer")]
        public async Task<ActionResult> CompleteFundingTransfer(int id)
        {
            try
            {
                var success = await _fundManagementService.CompleteFundingTransferAsync(id);

                if (success)
                {
                    return Ok(new { message = "تم إكمال التحويل بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "لا يمكن إكمال التحويل" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إكمال التحويل التمويلي: {TransferId}", id);
                return StatusCode(500, new { message = "خطأ في إكمال التحويل التمويلي" });
            }
        }

        /// <summary>
        /// تقرير حركة الصندوق
        /// </summary>
        [HttpGet("{id}/movement-report")]
        [HasPermission("finance.reports.view")]
        public async Task<ActionResult<FundMovementReport>> GetFundMovementReport(
            int id,
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            try
            {
                var report = await _fundManagementService.GenerateFundMovementReportAsync(id, fromDate, toDate);
                return Ok(report);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير حركة الصندوق: {FundId}", id);
                return StatusCode(500, new { message = "خطأ في إنتاج تقرير حركة الصندوق" });
            }
        }

        /// <summary>
        /// تقرير أرصدة الصناديق
        /// </summary>
        [HttpGet("balances-report")]
        [HasPermission("finance.reports.view")]
        public async Task<ActionResult<List<FundBalanceReport>>> GetFundBalancesReport([FromQuery] DateTime? asOfDate = null)
        {
            try
            {
                var report = await _fundManagementService.GetFundBalancesReportAsync(asOfDate ?? DateTime.Now);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير أرصدة الصناديق");
                return StatusCode(500, new { message = "خطأ في إنتاج تقرير أرصدة الصناديق" });
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("user_id")?.Value;
            return int.TryParse(userIdClaim, out int userId) ? userId : 0;
        }
    }
}
