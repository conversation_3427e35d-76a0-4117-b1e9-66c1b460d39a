-- إنشاء جداول نظام بنود الصرف الديناميكي
-- Create Expense Management Tables
-- متوافق مع النماذج الموجودة في Backend والجداول المالية الحالية

USE [databasetasks]
GO

PRINT '🚀 بدء إنشاء جداول نظام بنود الصرف الديناميكي...'
PRINT '📋 التحقق من الجداول المرجعية المطلوبة...'

-- التحقق من وجود الجداول المرجعية المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'chart_of_accounts')
BEGIN
    PRINT '❌ خطأ: جدول chart_of_accounts غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'journal_entries')
BEGIN
    PRINT '❌ خطأ: جدول journal_entries غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'budgets')
BEGIN
    PRINT '❌ خطأ: جدول budgets غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'budget_lines')
BEGIN
    PRINT '❌ خطأ: جدول budget_lines غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

PRINT '✅ جميع الجداول المرجعية موجودة. المتابعة...'

-- 1. جدول بنود الصرف الشجرية
PRINT '📋 إنشاء جدول بنود الصرف الشجرية (expense_item_types)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'expense_item_types')
BEGIN
    CREATE TABLE [dbo].[expense_item_types] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [name] NVARCHAR(100) NOT NULL,
        [name_arabic] NVARCHAR(200) NOT NULL,
        [description] NVARCHAR(500) NULL,
        [code] NVARCHAR(50) NOT NULL UNIQUE,
        [parent_id] INT NULL,
        [level] INT NOT NULL DEFAULT 0,
        [sort_order] INT NOT NULL DEFAULT 0,
        [is_active] BIT NOT NULL DEFAULT 1,
        [is_leaf] BIT NOT NULL DEFAULT 1,
        [data_schema] NVARCHAR(MAX) NULL, -- JSON schema for dynamic fields
        [approval_workflow] NVARCHAR(50) NULL, -- simple, multi_level, department_based
        [requires_budget_check] BIT NOT NULL DEFAULT 1,
        [default_account_id] INT NULL,
        [max_amount] DECIMAL(18,2) NULL,
        [min_amount] DECIMAL(18,2) NULL,
        [icon] NVARCHAR(100) NULL,
        [color] NVARCHAR(20) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_by] INT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_expense_item_types_parent] FOREIGN KEY ([parent_id]) REFERENCES [expense_item_types]([id]),
        CONSTRAINT [FK_expense_item_types_default_account] FOREIGN KEY ([default_account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_expense_item_types_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expense_item_types_updated_by] FOREIGN KEY ([updated_by]) REFERENCES [users]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_expense_item_types_parent_id] ON [expense_item_types]([parent_id]);
    CREATE INDEX [IX_expense_item_types_level] ON [expense_item_types]([level]);
    CREATE INDEX [IX_expense_item_types_is_active] ON [expense_item_types]([is_active]);
    CREATE INDEX [IX_expense_item_types_code] ON [expense_item_types]([code]);
    
    PRINT '✅ تم إنشاء جدول expense_item_types بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول expense_item_types موجود مسبقاً'
END

-- 2. جدول قوالب الصرف
PRINT '📋 إنشاء جدول قوالب الصرف (expense_templates)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'expense_templates')
BEGIN
    CREATE TABLE [dbo].[expense_templates] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [name] NVARCHAR(100) NOT NULL,
        [name_arabic] NVARCHAR(200) NOT NULL,
        [description] NVARCHAR(500) NULL,
        [expense_item_type_id] INT NOT NULL,
        [is_active] BIT NOT NULL DEFAULT 1,
        [is_default] BIT NOT NULL DEFAULT 0,
        [template_config] NVARCHAR(MAX) NULL, -- JSON configuration
        [approval_levels] NVARCHAR(MAX) NULL, -- JSON approval configuration
        [calculation_rules] NVARCHAR(MAX) NULL, -- JSON calculation rules
        [default_lines] NVARCHAR(MAX) NULL, -- JSON default expense lines
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_by] INT NULL,
        [updated_at] BIGINT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_expense_templates_expense_item_type] FOREIGN KEY ([expense_item_type_id]) REFERENCES [expense_item_types]([id]),
        CONSTRAINT [FK_expense_templates_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expense_templates_updated_by] FOREIGN KEY ([updated_by]) REFERENCES [users]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_expense_templates_expense_item_type_id] ON [expense_templates]([expense_item_type_id]);
    CREATE INDEX [IX_expense_templates_is_active] ON [expense_templates]([is_active]);
    CREATE INDEX [IX_expense_templates_is_default] ON [expense_templates]([is_default]);
    
    PRINT '✅ تم إنشاء جدول expense_templates بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول expense_templates موجود مسبقاً'
END

-- 3. جدول المصروفات
PRINT '📋 إنشاء جدول المصروفات (expenses)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'expenses')
BEGIN
    CREATE TABLE [dbo].[expenses] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [expense_number] NVARCHAR(50) NOT NULL UNIQUE,
        [title] NVARCHAR(200) NOT NULL,
        [description] NVARCHAR(1000) NULL,
        [expense_item_type_id] INT NOT NULL,
        [expense_template_id] INT NULL,
        [requested_by] INT NOT NULL,
        [department_id] INT NULL,
        [cost_center_id] INT NULL,
        [task_id] INT NULL, -- استخدام task_id بدلاً من project_id
        [total_amount] DECIMAL(18,2) NOT NULL,
        [currency] NVARCHAR(3) NOT NULL DEFAULT 'SAR',
        [exchange_rate] DECIMAL(10,4) NOT NULL DEFAULT 1.0,
        [expense_date] DATETIME NOT NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'draft', -- draft, submitted, approved, rejected, paid, cancelled
        [priority] NVARCHAR(20) NOT NULL DEFAULT 'normal', -- low, normal, high, urgent
        [due_date] DATETIME NULL,
        [approval_workflow] NVARCHAR(MAX) NULL, -- JSON workflow state
        [custom_fields] NVARCHAR(MAX) NULL, -- JSON custom field values
        [budget_id] INT NULL,
        [budget_line_id] INT NULL,
        [journal_entry_id] INT NULL,
        [payment_method] NVARCHAR(50) NULL,
        [payment_reference] NVARCHAR(100) NULL,
        [payment_date] DATETIME NULL,
        [paid_amount] DECIMAL(18,2) NULL,
        [rejection_reason] NVARCHAR(500) NULL,
        [rejected_by] INT NULL,
        [rejected_at] BIGINT NULL,
        [approved_by] INT NULL,
        [approved_at] BIGINT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_by] INT NULL,
        [updated_at] BIGINT NULL,
        [submitted_at] BIGINT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_expenses_expense_item_type] FOREIGN KEY ([expense_item_type_id]) REFERENCES [expense_item_types]([id]),
        CONSTRAINT [FK_expenses_expense_template] FOREIGN KEY ([expense_template_id]) REFERENCES [expense_templates]([id]),
        CONSTRAINT [FK_expenses_requested_by] FOREIGN KEY ([requested_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expenses_department] FOREIGN KEY ([department_id]) REFERENCES [departments]([id]),
        CONSTRAINT [FK_expenses_task] FOREIGN KEY ([task_id]) REFERENCES [tasks]([id]),
        CONSTRAINT [FK_expenses_budget] FOREIGN KEY ([budget_id]) REFERENCES [budgets]([id]),
        CONSTRAINT [FK_expenses_budget_line] FOREIGN KEY ([budget_line_id]) REFERENCES [budget_lines]([id]),
        CONSTRAINT [FK_expenses_journal_entry] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_expenses_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expenses_updated_by] FOREIGN KEY ([updated_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expenses_approved_by] FOREIGN KEY ([approved_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expenses_rejected_by] FOREIGN KEY ([rejected_by]) REFERENCES [users]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_expenses_expense_number] ON [expenses]([expense_number]);
    CREATE INDEX [IX_expenses_status] ON [expenses]([status]);
    CREATE INDEX [IX_expenses_requested_by] ON [expenses]([requested_by]);
    CREATE INDEX [IX_expenses_department_id] ON [expenses]([department_id]);
    CREATE INDEX [IX_expenses_expense_date] ON [expenses]([expense_date]);
    CREATE INDEX [IX_expenses_total_amount] ON [expenses]([total_amount]);
    CREATE INDEX [IX_expenses_created_at] ON [expenses]([created_at]);
    
    PRINT '✅ تم إنشاء جدول expenses بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول expenses موجود مسبقاً'
END

-- 4. جدول خطوط المصروفات
PRINT '📋 إنشاء جدول خطوط المصروفات (expense_lines)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'expense_lines')
BEGIN
    CREATE TABLE [dbo].[expense_lines] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [expense_id] INT NOT NULL,
        [description] NVARCHAR(200) NOT NULL,
        [account_id] INT NOT NULL,
        [amount] DECIMAL(18,2) NOT NULL,
        [quantity] DECIMAL(10,3) NULL,
        [unit_price] DECIMAL(18,2) NULL,
        [unit] NVARCHAR(50) NULL,
        [tax_rate] DECIMAL(5,2) NULL,
        [tax_amount] DECIMAL(18,2) NULL,
        [discount_rate] DECIMAL(5,2) NULL,
        [discount_amount] DECIMAL(18,2) NULL,
        [net_amount] DECIMAL(18,2) NOT NULL,
        [cost_center_id] INT NULL,
        [task_id] INT NULL, -- استخدام task_id بدلاً من project_id
        [department_id] INT NULL,
        [reference] NVARCHAR(100) NULL,
        [line_date] DATETIME NULL,
        [sort_order] INT NOT NULL DEFAULT 0,
        [line_data] NVARCHAR(MAX) NULL, -- JSON additional data
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_expense_lines_expense] FOREIGN KEY ([expense_id]) REFERENCES [expenses]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_expense_lines_account] FOREIGN KEY ([account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_expense_lines_task] FOREIGN KEY ([task_id]) REFERENCES [tasks]([id]),
        CONSTRAINT [FK_expense_lines_department] FOREIGN KEY ([department_id]) REFERENCES [departments]([id])
    );

    -- Indexes
    CREATE INDEX [IX_expense_lines_expense_id] ON [expense_lines]([expense_id]);
    CREATE INDEX [IX_expense_lines_account_id] ON [expense_lines]([account_id]);
    CREATE INDEX [IX_expense_lines_sort_order] ON [expense_lines]([sort_order]);

    PRINT '✅ تم إنشاء جدول expense_lines بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول expense_lines موجود مسبقاً'
END

-- 5. جدول اعتمادات المصروفات
PRINT '📋 إنشاء جدول اعتمادات المصروفات (expense_approvals)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'expense_approvals')
BEGIN
    CREATE TABLE [dbo].[expense_approvals] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [expense_id] INT NOT NULL,
        [level] INT NOT NULL,
        [level_name] NVARCHAR(100) NOT NULL,
        [approver_id] INT NOT NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, approved, rejected, delegated
        [comments] NVARCHAR(500) NULL,
        [approved_at] BIGINT NULL,
        [rejected_at] BIGINT NULL,
        [delegated_to] INT NULL,
        [delegated_at] BIGINT NULL,
        [delegation_reason] NVARCHAR(500) NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_expense_approvals_expense] FOREIGN KEY ([expense_id]) REFERENCES [expenses]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_expense_approvals_approver] FOREIGN KEY ([approver_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_expense_approvals_delegated_to] FOREIGN KEY ([delegated_to]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_expense_approvals_expense_id] ON [expense_approvals]([expense_id]);
    CREATE INDEX [IX_expense_approvals_approver_id] ON [expense_approvals]([approver_id]);
    CREATE INDEX [IX_expense_approvals_status] ON [expense_approvals]([status]);
    CREATE INDEX [IX_expense_approvals_level] ON [expense_approvals]([level]);

    PRINT '✅ تم إنشاء جدول expense_approvals بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول expense_approvals موجود مسبقاً'
END

-- 6. جدول مرفقات المصروفات
PRINT '📋 إنشاء جدول مرفقات المصروفات (expense_attachments)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'expense_attachments')
BEGIN
    CREATE TABLE [dbo].[expense_attachments] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [expense_id] INT NOT NULL,
        [file_name] NVARCHAR(255) NOT NULL,
        [original_name] NVARCHAR(255) NOT NULL,
        [file_path] NVARCHAR(500) NOT NULL,
        [content_type] NVARCHAR(100) NOT NULL,
        [file_size] BIGINT NOT NULL,
        [description] NVARCHAR(200) NULL,
        [uploaded_by] INT NOT NULL,
        [uploaded_at] BIGINT NOT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_expense_attachments_expense] FOREIGN KEY ([expense_id]) REFERENCES [expenses]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_expense_attachments_uploaded_by] FOREIGN KEY ([uploaded_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_expense_attachments_expense_id] ON [expense_attachments]([expense_id]);
    CREATE INDEX [IX_expense_attachments_uploaded_by] ON [expense_attachments]([uploaded_by]);
    CREATE INDEX [IX_expense_attachments_uploaded_at] ON [expense_attachments]([uploaded_at]);

    PRINT '✅ تم إنشاء جدول expense_attachments بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول expense_attachments موجود مسبقاً'
END

PRINT '🎉 تم إنشاء جميع جداول نظام بنود الصرف الديناميكي بنجاح!'
PRINT ''
PRINT '📊 ملخص الجداول المنشأة:'
PRINT '   1. expense_item_types - بنود الصرف الشجرية'
PRINT '   2. expense_templates - قوالب الصرف'
PRINT '   3. expenses - المصروفات'
PRINT '   4. expense_lines - خطوط المصروفات'
PRINT '   5. expense_approvals - اعتمادات المصروفات'
PRINT '   6. expense_attachments - مرفقات المصروفات'
PRINT ''
PRINT '✅ النظام جاهز لاستقبال بيانات بنود الصرف الديناميكي!'
PRINT ''
PRINT '🔗 العلاقات المتوافقة:'
PRINT '   ✅ chart_of_accounts - للحسابات المحاسبية'
PRINT '   ✅ journal_entries - للقيود المحاسبية'
PRINT '   ✅ budgets/budget_lines - للميزانيات'
PRINT '   ✅ users - للمستخدمين'
PRINT '   ✅ departments - للأقسام'
PRINT '   ✅ tasks - للمشاريع'
PRINT ''
PRINT '⚠️ ملاحظة: تأكد من تشغيل سكريبتات إنشاء الجداول المالية أولاً'

GO
