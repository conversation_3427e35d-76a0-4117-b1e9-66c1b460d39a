using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج الصرف
    /// Expense Model
    /// </summary>
    [Table("expenses")]
    public class Expense
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Column("expense_number")]
        public string ExpenseNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Column("title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("expense_item_type_id")]
        public int ExpenseItemTypeId { get; set; }

        [Column("expense_template_id")]
        public int? ExpenseTemplateId { get; set; }

        [Required]
        [Column("requested_by")]
        public int RequestedBy { get; set; }

        [Column("department_id")]
        public int? DepartmentId { get; set; }

        [Column("cost_center_id")]
        public int? CostCenterId { get; set; }

        [Column("task_id")]
        public int? TaskId { get; set; }

        [Required]
        [Column("total_amount", TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [Required]
        [StringLength(3)]
        [Column("currency")]
        public string Currency { get; set; } = "SAR";

        [Column("exchange_rate", TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1.0m;

        [Required]
        [Column("expense_date")]
        public DateTime ExpenseDate { get; set; }

        [Required]
        [StringLength(20)]
        [Column("status")]
        public string Status { get; set; } = "draft"; // draft, submitted, approved, rejected, paid, cancelled

        [StringLength(20)]
        [Column("priority")]
        public string Priority { get; set; } = "normal"; // low, normal, high, urgent

        [Column("due_date")]
        public DateTime? DueDate { get; set; }

        [Column("approval_workflow", TypeName = "nvarchar(max)")]
        public string? ApprovalWorkflow { get; set; } // JSON workflow state

        [Column("custom_fields", TypeName = "nvarchar(max)")]
        public string? CustomFields { get; set; } // JSON custom field values

        [Column("budget_id")]
        public int? BudgetId { get; set; }

        [Column("budget_line_id")]
        public int? BudgetLineId { get; set; }

        [Column("journal_entry_id")]
        public int? JournalEntryId { get; set; }

        [Column("payment_method")]
        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [Column("payment_reference")]
        [StringLength(100)]
        public string? PaymentReference { get; set; }

        [Column("payment_date")]
        public DateTime? PaymentDate { get; set; }

        [Column("paid_amount", TypeName = "decimal(18,2)")]
        public decimal? PaidAmount { get; set; }

        [StringLength(500)]
        [Column("rejection_reason")]
        public string? RejectionReason { get; set; }

        [Column("rejected_by")]
        public int? RejectedBy { get; set; }

        [Column("rejected_at")]
        public long? RejectedAt { get; set; }

        [Column("approved_by")]
        public int? ApprovedBy { get; set; }

        [Column("approved_at")]
        public long? ApprovedAt { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        [Column("submitted_at")]
        public long? SubmittedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ExpenseItemTypeId")]
        public virtual ExpenseItemType ExpenseItemType { get; set; } = null!;

        [ForeignKey("ExpenseTemplateId")]
        public virtual ExpenseTemplate? ExpenseTemplate { get; set; }

        [ForeignKey("RequestedBy")]
        public virtual User Requester { get; set; } = null!;

        [ForeignKey("DepartmentId")]
        public virtual Department? Department { get; set; }

        [ForeignKey("TaskId")]
        public virtual Task? Task { get; set; }

        [ForeignKey("BudgetId")]
        public virtual Budget? Budget { get; set; }

        [ForeignKey("BudgetLineId")]
        public virtual BudgetLine? BudgetLine { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("UpdatedBy")]
        public virtual User? Updater { get; set; }

        [ForeignKey("ApprovedBy")]
        public virtual User? Approver { get; set; }

        [ForeignKey("RejectedBy")]
        public virtual User? Rejector { get; set; }

        public virtual ICollection<ExpenseLine> ExpenseLines { get; set; } = new List<ExpenseLine>();
        public virtual ICollection<ExpenseApproval> ExpenseApprovals { get; set; } = new List<ExpenseApproval>();
        public virtual ICollection<ExpenseAttachment> ExpenseAttachments { get; set; } = new List<ExpenseAttachment>();

        // Calculated Properties
        [NotMapped]
        public bool IsEditable => Status == "draft";

        [NotMapped]
        public bool CanBeSubmitted => Status == "draft" && ExpenseLines.Any() && TotalAmount > 0;

        [NotMapped]
        public bool CanBeApproved => Status == "submitted";

        [NotMapped]
        public bool CanBeRejected => Status == "submitted";

        [NotMapped]
        public bool CanBePaid => Status == "approved" && !IsPaid;

        [NotMapped]
        public bool IsPaid => Status == "paid" || (PaidAmount.HasValue && PaidAmount >= TotalAmount);

        [NotMapped]
        public decimal RemainingAmount => TotalAmount - (PaidAmount ?? 0);

        [NotMapped]
        public Dictionary<string, object>? ParsedCustomFields
        {
            get
            {
                if (string.IsNullOrEmpty(CustomFields))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(CustomFields);
                }
                catch
                {
                    return null;
                }
            }
        }

        [NotMapped]
        public ExpenseWorkflowState? ParsedApprovalWorkflow
        {
            get
            {
                if (string.IsNullOrEmpty(ApprovalWorkflow))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<ExpenseWorkflowState>(ApprovalWorkflow);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// تحديد الحقول المخصصة
        /// </summary>
        public void SetCustomFields(Dictionary<string, object> fields)
        {
            CustomFields = System.Text.Json.JsonSerializer.Serialize(fields);
        }

        /// <summary>
        /// تحديد حالة سير العمل
        /// </summary>
        public void SetApprovalWorkflow(ExpenseWorkflowState workflow)
        {
            ApprovalWorkflow = System.Text.Json.JsonSerializer.Serialize(workflow);
        }

        /// <summary>
        /// حساب المجموع من الخطوط
        /// </summary>
        public void CalculateTotal()
        {
            TotalAmount = ExpenseLines.Sum(line => line.Amount);
        }

        /// <summary>
        /// توليد رقم الصرف
        /// </summary>
        public static string GenerateExpenseNumber(string prefix = "EXP")
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var random = new Random().Next(100, 999);
            return $"{prefix}-{timestamp}-{random}";
        }
    }

    /// <summary>
    /// حالة سير عمل الصرف
    /// </summary>
    public class ExpenseWorkflowState
    {
        public int CurrentLevel { get; set; } = 0;
        public List<ExpenseWorkflowStep> Steps { get; set; } = new();
        public bool IsCompleted { get; set; } = false;
        public DateTime? CompletedAt { get; set; }
        public string? CurrentStepName { get; set; }
        public List<int> PendingApprovers { get; set; } = new();
    }

    /// <summary>
    /// خطوة سير عمل الصرف
    /// </summary>
    public class ExpenseWorkflowStep
    {
        public int Level { get; set; }
        public string StepName { get; set; } = string.Empty;
        public string Status { get; set; } = "pending"; // pending, approved, rejected, skipped
        public int? ApproverId { get; set; }
        public string? ApproverName { get; set; }
        public DateTime? ActionDate { get; set; }
        public string? Comments { get; set; }
    }
}
