using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services;
using webApi.Services.Finance;
using webApi.DTOs.Finance;

namespace webApi.Controllers.Finance
{
    /// <summary>
    /// تحكم في التقارير المالية
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FinancialReportsController : ControllerBase
    {
        private readonly IFinancialReportingService _reportingService;
        private readonly ILogger<FinancialReportsController> _logger;
        private readonly ILoggingService _loggingService;

        public FinancialReportsController(
            IFinancialReportingService reportingService,
            ILogger<FinancialReportsController> logger,
            ILoggingService loggingService)
        {
            _reportingService = reportingService;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// تقرير الميزانية العمومية
        /// </summary>
        [HttpPost("balance-sheet")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<BalanceSheetReport>> GenerateBalanceSheet([FromBody] BalanceSheetRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateBalanceSheetAsync(request.AsOfDate, request.DepartmentId);

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "توليد تقرير الميزانية العمومية",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير الميزانية العمومية كما في {request.AsOfDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير الميزانية العمومية");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير قائمة الدخل
        /// </summary>
        [HttpPost("income-statement")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<IncomeStatementReport>> GenerateIncomeStatement([FromBody] IncomeStatementRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateIncomeStatementAsync(
                    request.StartDate, 
                    request.EndDate, 
                    request.DepartmentId);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير قائمة الدخل",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير قائمة الدخل للفترة {request.StartDate:yyyy-MM-dd} - {request.EndDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير قائمة الدخل");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير التدفقات النقدية
        /// </summary>
        [HttpPost("cash-flow")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<CashFlowReport>> GenerateCashFlowReport([FromBody] CashFlowRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateCashFlowReportAsync(
                    request.StartDate, 
                    request.EndDate, 
                    request.FundId);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير التدفقات النقدية",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير التدفقات النقدية للفترة {request.StartDate:yyyy-MM-dd} - {request.EndDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير التدفقات النقدية");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير أرصدة الصناديق
        /// </summary>
        [HttpPost("fund-balance")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<DTOs.Finance.FundBalanceReport>> GenerateFundBalanceReport([FromBody] FundBalanceRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateFundBalanceReportAsync(request.AsOfDate, request.FundId);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير أرصدة الصناديق",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير أرصدة الصناديق كما في {request.AsOfDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير أرصدة الصناديق");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير المصروفات
        /// </summary>
        [HttpPost("expense-report")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ExpenseReport>> GenerateExpenseReport([FromBody] ExpenseReportRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateExpenseReportAsync(
                    request.StartDate, 
                    request.EndDate, 
                    request.DepartmentId);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير المصروفات",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير المصروفات للفترة {request.StartDate:yyyy-MM-dd} - {request.EndDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير المصروفات");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير الرواتب
        /// </summary>
        [HttpPost("payroll-report")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<PayrollReport>> GeneratePayrollReport([FromBody] PayrollReportRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GeneratePayrollReportAsync(
                    request.StartDate, 
                    request.EndDate, 
                    request.DepartmentId);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير الرواتب",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير الرواتب للفترة {request.StartDate:yyyy-MM-dd} - {request.EndDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير الرواتب");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير الميزانية مقابل الفعلي
        /// </summary>
        [HttpPost("budget-vs-actual")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<BudgetVsActualReport>> GenerateBudgetVsActualReport([FromBody] BudgetVsActualRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateBudgetVsActualReportAsync(
                    request.StartDate, 
                    request.EndDate, 
                    request.DepartmentId);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير الميزانية مقابل الفعلي",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير الميزانية مقابل الفعلي للفترة {request.StartDate:yyyy-MM-dd} - {request.EndDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير الميزانية مقابل الفعلي");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تقرير أرصدة الحسابات
        /// </summary>
        [HttpPost("account-balance")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<AccountBalanceReport>> GenerateAccountBalanceReport([FromBody] AccountBalanceRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var report = await _reportingService.GenerateAccountBalanceReportAsync(request.AsOfDate, request.AccountType);

                await _loggingService.LogActivityAsync(
                    "توليد تقرير أرصدة الحسابات",
                    "FinancialReport",
                    0,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم توليد تقرير أرصدة الحسابات كما في {request.AsOfDate:yyyy-MM-dd}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير أرصدة الحسابات");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على قائمة التقارير المتاحة
        /// </summary>
        [HttpGet("available-reports")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<AvailableReport>> GetAvailableReports()
        {
            try
            {
                var reports = new List<AvailableReport>
                {
                    new AvailableReport
                    {
                        Id = "balance-sheet",
                        Name = "الميزانية العمومية",
                        Description = "تقرير يعرض الأصول والخصوم وحقوق الملكية في تاريخ محدد",
                        Category = "financial",
                        RequiredParameters = new[] { "asOfDate" },
                        OptionalParameters = new[] { "departmentId", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "income-statement",
                        Name = "قائمة الدخل",
                        Description = "تقرير يعرض الإيرادات والمصروفات وصافي الدخل لفترة محددة",
                        Category = "financial",
                        RequiredParameters = new[] { "startDate", "endDate" },
                        OptionalParameters = new[] { "departmentId", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "cash-flow",
                        Name = "التدفقات النقدية",
                        Description = "تقرير يعرض التدفقات النقدية الداخلة والخارجة",
                        Category = "financial",
                        RequiredParameters = new[] { "startDate", "endDate" },
                        OptionalParameters = new[] { "fundId", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "fund-balance",
                        Name = "أرصدة الصناديق",
                        Description = "تقرير يعرض أرصدة جميع الصناديق",
                        Category = "financial",
                        RequiredParameters = new[] { "asOfDate" },
                        OptionalParameters = new[] { "fundId", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "expense-report",
                        Name = "تقرير المصروفات",
                        Description = "تقرير تفصيلي للمصروفات حسب النوع والقسم والحالة",
                        Category = "operational",
                        RequiredParameters = new[] { "startDate", "endDate" },
                        OptionalParameters = new[] { "departmentId", "expenseType", "status", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "payroll-report",
                        Name = "تقرير الرواتب",
                        Description = "تقرير تفصيلي للرواتب حسب القسم والموظف",
                        Category = "hr",
                        RequiredParameters = new[] { "startDate", "endDate" },
                        OptionalParameters = new[] { "departmentId", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "budget-vs-actual",
                        Name = "الميزانية مقابل الفعلي",
                        Description = "تقرير مقارنة بين الميزانية المخططة والمصروفات الفعلية",
                        Category = "financial",
                        RequiredParameters = new[] { "startDate", "endDate" },
                        OptionalParameters = new[] { "departmentId", "format" }
                    },
                    new AvailableReport
                    {
                        Id = "account-balance",
                        Name = "أرصدة الحسابات",
                        Description = "تقرير يعرض أرصدة جميع الحسابات أو نوع محدد من الحسابات",
                        Category = "financial",
                        RequiredParameters = new[] { "asOfDate" },
                        OptionalParameters = new[] { "accountType", "format" }
                    }
                };

                return Ok(reports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قائمة التقارير المتاحة");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }
    }

    /// <summary>
    /// تقرير متاح
    /// </summary>
    public class AvailableReport
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string[] RequiredParameters { get; set; } = Array.Empty<string>();
        public string[] OptionalParameters { get; set; } = Array.Empty<string>();
    }
}
