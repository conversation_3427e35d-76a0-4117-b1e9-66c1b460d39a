using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج الحساب الشخصي
    /// Personal Account Model
    /// </summary>
    [Table("personal_accounts")]
    public class PersonalAccount
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("person_id")]
        public int PersonId { get; set; }

        [Required]
        [StringLength(20)]
        [Column("account_code")]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Column("account_name")]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column("account_type")]
        public string AccountType { get; set; } = string.Empty; // advance, salary, expense, general

        [Column("current_balance", TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column("credit_limit", TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0;

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("PersonId")]
        public virtual User Person { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<PersonalAccountTransaction> Transactions { get; set; } = new List<PersonalAccountTransaction>();

        public virtual ICollection<FundingTransfer> TargetTransfers { get; set; } = new List<FundingTransfer>();

        // Calculated Properties
        [NotMapped]
        public decimal AvailableBalance => CurrentBalance + CreditLimit;

        [NotMapped]
        public bool IsOverLimit => CurrentBalance < -CreditLimit;

        [NotMapped]
        public string DisplayName => $"{AccountCode} - {AccountName}";

        [NotMapped]
        public string AccountTypeText => AccountType switch
        {
            "advance" => "سلفة",
            "salary" => "راتب",
            "expense" => "مصروفات",
            "general" => "عام",
            _ => "غير محدد"
        };

        [NotMapped]
        public string BalanceStatus => CurrentBalance >= 0 ? "دائن" : "مدين";
    }
}
