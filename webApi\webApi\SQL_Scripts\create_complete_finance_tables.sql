-- إنشاء جميع الجداول المالية المفقودة
-- Create Complete Finance Tables
-- متوافق مع النماذج الموجودة في Backend (15 نموذج)

USE [databasetasks]
GO

PRINT '🚀 بدء إنشاء جميع الجداول المالية المفقودة...'
PRINT '📋 التحقق من الجداول الأساسية المطلوبة...'

-- التحقق من وجود الجداول الأساسية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users')
BEGIN
    PRINT '❌ خطأ: جدول users غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'departments')
BEGIN
    PRINT '❌ خطأ: جدول departments غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tasks')
BEGIN
    PRINT '❌ خطأ: جدول tasks غير موجود. يجب إنشاؤه أولاً.'
    RETURN
END

PRINT '✅ جميع الجداول الأساسية موجودة. المتابعة...'

-- ===================================================================
-- 1. جدول دليل الحسابات (Chart of Accounts)
-- ===================================================================
PRINT '📋 إنشاء جدول دليل الحسابات (chart_of_accounts)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'chart_of_accounts')
BEGIN
    CREATE TABLE [dbo].[chart_of_accounts] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [account_code] NVARCHAR(20) NOT NULL UNIQUE,
        [account_name] NVARCHAR(100) NOT NULL,
        [account_type] NVARCHAR(50) NOT NULL, -- Asset, Liability, Equity, Revenue, Expense
        [parent_account_id] INT NULL,
        [account_level] INT NOT NULL DEFAULT 1,
        [balance_type] NVARCHAR(10) NOT NULL, -- Debit, Credit
        [current_balance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [is_active] BIT NOT NULL DEFAULT 1,
        [is_system_account] BIT NOT NULL DEFAULT 0,
        [linked_person_id] INT NULL,
        [description] NVARCHAR(500) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_chart_of_accounts_parent] FOREIGN KEY ([parent_account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_chart_of_accounts_linked_person] FOREIGN KEY ([linked_person_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_chart_of_accounts_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_chart_of_accounts_code] ON [chart_of_accounts]([account_code]);
    CREATE INDEX [IX_chart_of_accounts_type] ON [chart_of_accounts]([account_type]);
    CREATE INDEX [IX_chart_of_accounts_parent] ON [chart_of_accounts]([parent_account_id]);
    CREATE INDEX [IX_chart_of_accounts_active] ON [chart_of_accounts]([is_active]);
    
    PRINT '✅ تم إنشاء جدول chart_of_accounts بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول chart_of_accounts موجود مسبقاً'
END

-- ===================================================================
-- 2. جدول القيود اليومية (Journal Entries)
-- ===================================================================
PRINT '📋 إنشاء جدول القيود اليومية (journal_entries)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'journal_entries')
BEGIN
    CREATE TABLE [dbo].[journal_entries] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [entry_number] NVARCHAR(50) NOT NULL UNIQUE,
        [entry_date] DATETIME NOT NULL,
        [description] NVARCHAR(500) NULL,
        [total_debit] DECIMAL(18,2) NOT NULL,
        [total_credit] DECIMAL(18,2) NOT NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'draft', -- draft, posted, cancelled
        [reference_type] NVARCHAR(50) NULL, -- expense, payment, transfer, etc.
        [reference_id] INT NULL,
        [reference_number] NVARCHAR(100) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [posted_by] INT NULL,
        [posted_at] BIGINT NULL,
        [updated_at] BIGINT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_journal_entries_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_journal_entries_posted_by] FOREIGN KEY ([posted_by]) REFERENCES [users]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_journal_entries_number] ON [journal_entries]([entry_number]);
    CREATE INDEX [IX_journal_entries_date] ON [journal_entries]([entry_date]);
    CREATE INDEX [IX_journal_entries_status] ON [journal_entries]([status]);
    CREATE INDEX [IX_journal_entries_reference] ON [journal_entries]([reference_type], [reference_id]);
    
    PRINT '✅ تم إنشاء جدول journal_entries بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول journal_entries موجود مسبقاً'
END

-- ===================================================================
-- 3. جدول خطوط القيود اليومية (Journal Entry Lines)
-- ===================================================================
PRINT '📋 إنشاء جدول خطوط القيود اليومية (journal_entry_lines)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'journal_entry_lines')
BEGIN
    CREATE TABLE [dbo].[journal_entry_lines] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [journal_entry_id] INT NOT NULL,
        [account_id] INT NOT NULL,
        [description] NVARCHAR(200) NULL,
        [debit_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [credit_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [cost_center_id] INT NULL,
        [task_id] INT NULL, -- استخدام task_id بدلاً من project_id
        [line_order] INT NOT NULL DEFAULT 1,
        [created_at] BIGINT NOT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_journal_entry_lines_entry] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_journal_entry_lines_account] FOREIGN KEY ([account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_journal_entry_lines_cost_center] FOREIGN KEY ([cost_center_id]) REFERENCES [departments]([id]),
        CONSTRAINT [FK_journal_entry_lines_task] FOREIGN KEY ([task_id]) REFERENCES [tasks]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_journal_entry_lines_entry] ON [journal_entry_lines]([journal_entry_id]);
    CREATE INDEX [IX_journal_entry_lines_account] ON [journal_entry_lines]([account_id]);
    CREATE INDEX [IX_journal_entry_lines_order] ON [journal_entry_lines]([line_order]);
    
    PRINT '✅ تم إنشاء جدول journal_entry_lines بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول journal_entry_lines موجود مسبقاً'
END

-- ===================================================================
-- 4. جدول الميزانيات (Budgets)
-- ===================================================================
PRINT '📋 إنشاء جدول الميزانيات (budgets)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'budgets')
BEGIN
    CREATE TABLE [dbo].[budgets] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [budget_name] NVARCHAR(100) NOT NULL,
        [fiscal_year] NVARCHAR(10) NOT NULL,
        [start_date] DATETIME NOT NULL,
        [end_date] DATETIME NOT NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'draft', -- draft, active, closed
        [total_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [spent_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [committed_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [is_control_enabled] BIT NOT NULL DEFAULT 0,
        [description] NVARCHAR(500) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [approved_by] INT NULL,
        [approved_at] BIGINT NULL,
        [updated_at] BIGINT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_budgets_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_budgets_approved_by] FOREIGN KEY ([approved_by]) REFERENCES [users]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_budgets_fiscal_year] ON [budgets]([fiscal_year]);
    CREATE INDEX [IX_budgets_status] ON [budgets]([status]);
    CREATE INDEX [IX_budgets_dates] ON [budgets]([start_date], [end_date]);
    
    PRINT '✅ تم إنشاء جدول budgets بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول budgets موجود مسبقاً'
END

-- ===================================================================
-- 5. جدول خطوط الميزانية (Budget Lines)
-- ===================================================================
PRINT '📋 إنشاء جدول خطوط الميزانية (budget_lines)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'budget_lines')
BEGIN
    CREATE TABLE [dbo].[budget_lines] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [budget_id] INT NOT NULL,
        [account_id] INT NOT NULL,
        [cost_center_id] INT NULL,
        [task_id] INT NULL, -- استخدام task_id بدلاً من project_id
        [allocated_amount] DECIMAL(18,2) NOT NULL,
        [spent_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [committed_amount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [description] NVARCHAR(200) NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,
        
        -- Foreign Keys
        CONSTRAINT [FK_budget_lines_budget] FOREIGN KEY ([budget_id]) REFERENCES [budgets]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_budget_lines_account] FOREIGN KEY ([account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_budget_lines_cost_center] FOREIGN KEY ([cost_center_id]) REFERENCES [departments]([id]),
        CONSTRAINT [FK_budget_lines_task] FOREIGN KEY ([task_id]) REFERENCES [tasks]([id])
    );
    
    -- Indexes
    CREATE INDEX [IX_budget_lines_budget] ON [budget_lines]([budget_id]);
    CREATE INDEX [IX_budget_lines_account] ON [budget_lines]([account_id]);
    CREATE INDEX [IX_budget_lines_cost_center] ON [budget_lines]([cost_center_id]);
    
    PRINT '✅ تم إنشاء جدول budget_lines بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول budget_lines موجود مسبقاً'
END

-- ===================================================================
-- 6. جدول الصناديق (Funds)
-- ===================================================================
PRINT '📋 إنشاء جدول الصناديق (funds)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'funds')
BEGIN
    CREATE TABLE [dbo].[funds] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [fund_name] NVARCHAR(100) NOT NULL,
        [fund_code] NVARCHAR(20) NOT NULL UNIQUE,
        [account_id] INT NOT NULL,
        [location_id] INT NULL,
        [legal_entity_id] INT NULL,
        [current_balance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [opening_balance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [is_active] BIT NOT NULL DEFAULT 1,
        [is_main_fund] BIT NOT NULL DEFAULT 0,
        [fund_type] NVARCHAR(50) NOT NULL DEFAULT 'cash', -- cash, bank, petty_cash
        [description] NVARCHAR(500) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_funds_account] FOREIGN KEY ([account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_funds_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_funds_code] ON [funds]([fund_code]);
    CREATE INDEX [IX_funds_type] ON [funds]([fund_type]);
    CREATE INDEX [IX_funds_active] ON [funds]([is_active]);

    PRINT '✅ تم إنشاء جدول funds بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول funds موجود مسبقاً'
END

-- ===================================================================
-- 7. جدول حركات الصناديق (Fund Transactions)
-- ===================================================================
PRINT '📋 إنشاء جدول حركات الصناديق (fund_transactions)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'fund_transactions')
BEGIN
    CREATE TABLE [dbo].[fund_transactions] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [fund_id] INT NOT NULL,
        [transaction_type] NVARCHAR(20) NOT NULL, -- deposit, withdrawal, transfer_in, transfer_out
        [amount] DECIMAL(18,2) NOT NULL,
        [balance_after] DECIMAL(18,2) NOT NULL,
        [reference_number] NVARCHAR(100) NULL,
        [description] NVARCHAR(200) NULL,
        [transaction_date] DATETIME NOT NULL,
        [journal_entry_id] INT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_fund_transactions_fund] FOREIGN KEY ([fund_id]) REFERENCES [funds]([id]),
        CONSTRAINT [FK_fund_transactions_journal] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_fund_transactions_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_fund_transactions_fund] ON [fund_transactions]([fund_id]);
    CREATE INDEX [IX_fund_transactions_type] ON [fund_transactions]([transaction_type]);
    CREATE INDEX [IX_fund_transactions_date] ON [fund_transactions]([transaction_date]);

    PRINT '✅ تم إنشاء جدول fund_transactions بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول fund_transactions موجود مسبقاً'
END

-- ===================================================================
-- 8. جدول التحويلات التمويلية (Funding Transfers)
-- ===================================================================
PRINT '📋 إنشاء جدول التحويلات التمويلية (funding_transfers)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'funding_transfers')
BEGIN
    CREATE TABLE [dbo].[funding_transfers] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [source_fund_id] INT NOT NULL,
        [target_fund_id] INT NOT NULL,
        [source_account_id] INT NOT NULL,
        [target_account_id] INT NOT NULL,
        [amount] DECIMAL(18,2) NOT NULL,
        [transfer_date] DATETIME NOT NULL,
        [reference_number] NVARCHAR(100) NULL,
        [description] NVARCHAR(200) NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, completed, cancelled
        [journal_entry_id] INT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [approved_by] INT NULL,
        [approved_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_funding_transfers_source_fund] FOREIGN KEY ([source_fund_id]) REFERENCES [funds]([id]),
        CONSTRAINT [FK_funding_transfers_target_fund] FOREIGN KEY ([target_fund_id]) REFERENCES [funds]([id]),
        CONSTRAINT [FK_funding_transfers_source_account] FOREIGN KEY ([source_account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_funding_transfers_target_account] FOREIGN KEY ([target_account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_funding_transfers_journal] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_funding_transfers_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_funding_transfers_approved_by] FOREIGN KEY ([approved_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_funding_transfers_source] ON [funding_transfers]([source_fund_id]);
    CREATE INDEX [IX_funding_transfers_target] ON [funding_transfers]([target_fund_id]);
    CREATE INDEX [IX_funding_transfers_date] ON [funding_transfers]([transfer_date]);
    CREATE INDEX [IX_funding_transfers_status] ON [funding_transfers]([status]);

    PRINT '✅ تم إنشاء جدول funding_transfers بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول funding_transfers موجود مسبقاً'
END

-- ===================================================================
-- 9. جدول الحسابات الشخصية (Personal Accounts)
-- ===================================================================
PRINT '📋 إنشاء جدول الحسابات الشخصية (personal_accounts)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'personal_accounts')
BEGIN
    CREATE TABLE [dbo].[personal_accounts] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [person_id] INT NOT NULL,
        [account_code] NVARCHAR(20) NOT NULL,
        [account_name] NVARCHAR(100) NOT NULL,
        [account_type] NVARCHAR(50) NOT NULL, -- advance, salary, expense, general
        [current_balance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [credit_limit] DECIMAL(18,2) NULL,
        [is_active] BIT NOT NULL DEFAULT 1,
        [description] NVARCHAR(500) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_personal_accounts_person] FOREIGN KEY ([person_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_personal_accounts_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_personal_accounts_person] ON [personal_accounts]([person_id]);
    CREATE INDEX [IX_personal_accounts_code] ON [personal_accounts]([account_code]);
    CREATE INDEX [IX_personal_accounts_type] ON [personal_accounts]([account_type]);

    PRINT '✅ تم إنشاء جدول personal_accounts بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول personal_accounts موجود مسبقاً'
END

-- ===================================================================
-- 10. جدول حركات الحسابات الشخصية (Personal Account Transactions)
-- ===================================================================
PRINT '📋 إنشاء جدول حركات الحسابات الشخصية (personal_account_transactions)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'personal_account_transactions')
BEGIN
    CREATE TABLE [dbo].[personal_account_transactions] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [account_id] INT NOT NULL,
        [transaction_type] NVARCHAR(20) NOT NULL, -- debit, credit
        [amount] DECIMAL(18,2) NOT NULL,
        [balance_after] DECIMAL(18,2) NOT NULL,
        [description] NVARCHAR(200) NULL,
        [reference_type] NVARCHAR(50) NULL,
        [reference_id] INT NULL,
        [reference_number] NVARCHAR(100) NULL,
        [transaction_date] DATETIME NOT NULL,
        [journal_entry_id] INT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_personal_account_transactions_account] FOREIGN KEY ([account_id]) REFERENCES [personal_accounts]([id]),
        CONSTRAINT [FK_personal_account_transactions_journal] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_personal_account_transactions_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_personal_account_transactions_account] ON [personal_account_transactions]([account_id]);
    CREATE INDEX [IX_personal_account_transactions_type] ON [personal_account_transactions]([transaction_type]);
    CREATE INDEX [IX_personal_account_transactions_date] ON [personal_account_transactions]([transaction_date]);

    PRINT '✅ تم إنشاء جدول personal_account_transactions بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول personal_account_transactions موجود مسبقاً'
END

-- ===================================================================
-- 11. جدول الأصول (Assets)
-- ===================================================================
PRINT '📋 إنشاء جدول الأصول (assets)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'assets')
BEGIN
    CREATE TABLE [dbo].[assets] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [asset_code] NVARCHAR(50) NOT NULL UNIQUE,
        [asset_name] NVARCHAR(200) NOT NULL,
        [description] NVARCHAR(1000) NULL,
        [asset_type] NVARCHAR(50) NOT NULL, -- fixed, current, intangible
        [asset_category] NVARCHAR(100) NOT NULL,
        [asset_subcategory] NVARCHAR(100) NULL,
        [account_id] INT NOT NULL,
        [accumulated_depreciation_account_id] INT NULL,
        [depreciation_expense_account_id] INT NULL,
        [purchase_date] DATETIME NOT NULL,
        [purchase_cost] DECIMAL(18,2) NOT NULL,
        [current_value] DECIMAL(18,2) NOT NULL,
        [salvage_value] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [useful_life_years] INT NULL,
        [useful_life_units] INT NULL,
        [depreciation_method] NVARCHAR(50) NULL, -- straight_line, declining_balance, units_of_production
        [depreciation_rate] DECIMAL(5,2) NULL,
        [accumulated_depreciation] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [location_id] INT NULL,
        [department_id] INT NULL,
        [responsible_user_id] INT NULL,
        [supplier_id] INT NULL,
        [serial_number] NVARCHAR(100) NULL,
        [model] NVARCHAR(100) NULL,
        [manufacturer] NVARCHAR(100) NULL,
        [warranty_start_date] DATETIME NULL,
        [warranty_end_date] DATETIME NULL,
        [insurance_policy_number] NVARCHAR(100) NULL,
        [insurance_value] DECIMAL(18,2) NULL,
        [insurance_expiry_date] DATETIME NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'active', -- active, disposed, sold, damaged
        [is_active] BIT NOT NULL DEFAULT 1,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_assets_account] FOREIGN KEY ([account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_assets_accumulated_depreciation_account] FOREIGN KEY ([accumulated_depreciation_account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_assets_depreciation_expense_account] FOREIGN KEY ([depreciation_expense_account_id]) REFERENCES [chart_of_accounts]([id]),
        CONSTRAINT [FK_assets_department] FOREIGN KEY ([department_id]) REFERENCES [departments]([id]),
        CONSTRAINT [FK_assets_responsible_user] FOREIGN KEY ([responsible_user_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_assets_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_assets_code] ON [assets]([asset_code]);
    CREATE INDEX [IX_assets_type] ON [assets]([asset_type]);
    CREATE INDEX [IX_assets_category] ON [assets]([asset_category]);
    CREATE INDEX [IX_assets_status] ON [assets]([status]);
    CREATE INDEX [IX_assets_department] ON [assets]([department_id]);

    PRINT '✅ تم إنشاء جدول assets بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول assets موجود مسبقاً'
END

-- ===================================================================
-- 12. جدول إهلاك الأصول (Asset Depreciations)
-- ===================================================================
PRINT '📋 إنشاء جدول إهلاك الأصول (asset_depreciations)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'asset_depreciations')
BEGIN
    CREATE TABLE [dbo].[asset_depreciations] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [asset_id] INT NOT NULL,
        [depreciation_date] DATETIME NOT NULL,
        [depreciation_amount] DECIMAL(18,2) NOT NULL,
        [accumulated_depreciation] DECIMAL(18,2) NOT NULL,
        [book_value] DECIMAL(18,2) NOT NULL,
        [depreciation_method] NVARCHAR(50) NOT NULL,
        [calculation_basis] NVARCHAR(100) NULL,
        [journal_entry_id] INT NULL,
        [period_year] INT NOT NULL,
        [period_month] INT NOT NULL,
        [is_manual] BIT NOT NULL DEFAULT 0,
        [notes] NVARCHAR(500) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_asset_depreciations_asset] FOREIGN KEY ([asset_id]) REFERENCES [assets]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_asset_depreciations_journal] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_asset_depreciations_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_asset_depreciations_asset] ON [asset_depreciations]([asset_id]);
    CREATE INDEX [IX_asset_depreciations_date] ON [asset_depreciations]([depreciation_date]);
    CREATE INDEX [IX_asset_depreciations_period] ON [asset_depreciations]([period_year], [period_month]);

    PRINT '✅ تم إنشاء جدول asset_depreciations بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول asset_depreciations موجود مسبقاً'
END

-- ===================================================================
-- 13. جدول صيانة الأصول (Asset Maintenances)
-- ===================================================================
PRINT '📋 إنشاء جدول صيانة الأصول (asset_maintenances)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'asset_maintenances')
BEGIN
    CREATE TABLE [dbo].[asset_maintenances] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [asset_id] INT NOT NULL,
        [maintenance_type] NVARCHAR(50) NOT NULL, -- preventive, corrective, emergency
        [maintenance_date] DATETIME NOT NULL,
        [description] NVARCHAR(500) NOT NULL,
        [cost] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [vendor_id] INT NULL,
        [technician_id] INT NULL,
        [next_maintenance_date] DATETIME NULL,
        [maintenance_interval_days] INT NULL,
        [status] NVARCHAR(20) NOT NULL DEFAULT 'completed', -- scheduled, in_progress, completed, cancelled
        [priority] NVARCHAR(20) NOT NULL DEFAULT 'medium', -- low, medium, high, critical
        [journal_entry_id] INT NULL,
        [notes] NVARCHAR(1000) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_asset_maintenances_asset] FOREIGN KEY ([asset_id]) REFERENCES [assets]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_asset_maintenances_journal] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_asset_maintenances_technician] FOREIGN KEY ([technician_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_asset_maintenances_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_asset_maintenances_asset] ON [asset_maintenances]([asset_id]);
    CREATE INDEX [IX_asset_maintenances_date] ON [asset_maintenances]([maintenance_date]);
    CREATE INDEX [IX_asset_maintenances_type] ON [asset_maintenances]([maintenance_type]);
    CREATE INDEX [IX_asset_maintenances_status] ON [asset_maintenances]([status]);

    PRINT '✅ تم إنشاء جدول asset_maintenances بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول asset_maintenances موجود مسبقاً'
END

-- ===================================================================
-- 14. جدول تقييم الأصول (Asset Valuations)
-- ===================================================================
PRINT '📋 إنشاء جدول تقييم الأصول (asset_valuations)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'asset_valuations')
BEGIN
    CREATE TABLE [dbo].[asset_valuations] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [asset_id] INT NOT NULL,
        [valuation_date] DATETIME NOT NULL,
        [valuation_method] NVARCHAR(50) NOT NULL, -- market, cost, income
        [valuation_amount] DECIMAL(18,2) NOT NULL,
        [previous_value] DECIMAL(18,2) NULL,
        [variance_amount] DECIMAL(18,2) NULL,
        [variance_percentage] DECIMAL(5,2) NULL,
        [valuator_name] NVARCHAR(200) NULL,
        [reason] NVARCHAR(200) NULL,
        [journal_entry_id] INT NULL,
        [notes] NVARCHAR(1000) NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_asset_valuations_asset] FOREIGN KEY ([asset_id]) REFERENCES [assets]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_asset_valuations_journal] FOREIGN KEY ([journal_entry_id]) REFERENCES [journal_entries]([id]),
        CONSTRAINT [FK_asset_valuations_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_asset_valuations_asset] ON [asset_valuations]([asset_id]);
    CREATE INDEX [IX_asset_valuations_date] ON [asset_valuations]([valuation_date]);
    CREATE INDEX [IX_asset_valuations_method] ON [asset_valuations]([valuation_method]);

    PRINT '✅ تم إنشاء جدول asset_valuations بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول asset_valuations موجود مسبقاً'
END

-- ===================================================================
-- 15. جدول تحويل الأصول (Asset Transfers)
-- ===================================================================
PRINT '📋 إنشاء جدول تحويل الأصول (asset_transfers)...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'asset_transfers')
BEGIN
    CREATE TABLE [dbo].[asset_transfers] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [asset_id] INT NOT NULL,
        [transfer_date] DATETIME NOT NULL,
        [from_location_id] INT NULL,
        [to_location_id] INT NULL,
        [from_department_id] INT NULL,
        [to_department_id] INT NULL,
        [from_responsible_user_id] INT NULL,
        [to_responsible_user_id] INT NULL,
        [reason] NVARCHAR(500) NOT NULL,
        [condition_before] NVARCHAR(200) NULL,
        [condition_after] NVARCHAR(200) NULL,
        [notes] NVARCHAR(1000) NULL,
        [approved_by] INT NULL,
        [approved_at] BIGINT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL,

        -- Foreign Keys
        CONSTRAINT [FK_asset_transfers_asset] FOREIGN KEY ([asset_id]) REFERENCES [assets]([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_asset_transfers_from_department] FOREIGN KEY ([from_department_id]) REFERENCES [departments]([id]),
        CONSTRAINT [FK_asset_transfers_to_department] FOREIGN KEY ([to_department_id]) REFERENCES [departments]([id]),
        CONSTRAINT [FK_asset_transfers_from_user] FOREIGN KEY ([from_responsible_user_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_asset_transfers_to_user] FOREIGN KEY ([to_responsible_user_id]) REFERENCES [users]([id]),
        CONSTRAINT [FK_asset_transfers_approved_by] FOREIGN KEY ([approved_by]) REFERENCES [users]([id]),
        CONSTRAINT [FK_asset_transfers_created_by] FOREIGN KEY ([created_by]) REFERENCES [users]([id])
    );

    -- Indexes
    CREATE INDEX [IX_asset_transfers_asset] ON [asset_transfers]([asset_id]);
    CREATE INDEX [IX_asset_transfers_date] ON [asset_transfers]([transfer_date]);
    CREATE INDEX [IX_asset_transfers_from_dept] ON [asset_transfers]([from_department_id]);
    CREATE INDEX [IX_asset_transfers_to_dept] ON [asset_transfers]([to_department_id]);

    PRINT '✅ تم إنشاء جدول asset_transfers بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول asset_transfers موجود مسبقاً'
END

PRINT ''
PRINT '🎉 تم إنشاء جميع الجداول المالية بنجاح!'
PRINT ''
PRINT '📊 ملخص الجداول المنشأة (15 جدول):'
PRINT '   1. chart_of_accounts - دليل الحسابات'
PRINT '   2. journal_entries - القيود اليومية'
PRINT '   3. journal_entry_lines - خطوط القيود اليومية'
PRINT '   4. budgets - الميزانيات'
PRINT '   5. budget_lines - خطوط الميزانية'
PRINT '   6. funds - الصناديق'
PRINT '   7. fund_transactions - حركات الصناديق'
PRINT '   8. funding_transfers - التحويلات التمويلية'
PRINT '   9. personal_accounts - الحسابات الشخصية'
PRINT '   10. personal_account_transactions - حركات الحسابات الشخصية'
PRINT '   11. assets - الأصول'
PRINT '   12. asset_depreciations - إهلاك الأصول'
PRINT '   13. asset_maintenances - صيانة الأصول'
PRINT '   14. asset_valuations - تقييم الأصول'
PRINT '   15. asset_transfers - تحويل الأصول'
PRINT ''
PRINT '✅ تم إصلاح مراجع المشاريع:'
PRINT '   ❌ project_id → ✅ task_id (في journal_entry_lines و budget_lines)'
PRINT '   ❌ Project → ✅ Task (في Navigation Properties)'
PRINT ''
PRINT '🔗 جميع العلاقات متوافقة مع الجداول الموجودة:'
PRINT '   ✅ users - للمستخدمين'
PRINT '   ✅ departments - للأقسام'
PRINT '   ✅ tasks - للمهام (بدلاً من projects)'
PRINT ''
PRINT '⚠️ ملاحظة: يجب تحديث النماذج في Backend لتتطابق مع أسماء الحقول الصحيحة'

GO
