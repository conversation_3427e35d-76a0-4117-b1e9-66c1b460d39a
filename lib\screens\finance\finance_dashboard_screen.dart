import 'package:flutter/material.dart';
import 'accounts_screen.dart';
import 'funds_screen.dart';
import 'journal_entries_screen.dart';

/// شاشة لوحة تحكم الوحدة المالية
/// Finance Dashboard Screen
class FinanceDashboardScreen extends StatefulWidget {
  const FinanceDashboardScreen({Key? key}) : super(key: key);

  @override
  State<FinanceDashboardScreen> createState() => _FinanceDashboardScreenState();
}

class _FinanceDashboardScreenState extends State<FinanceDashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const FinanceOverviewTab(),
    const AccountsScreen(),
    const JournalEntriesScreen(),
    const FundsScreen(),
  ];

  final List<String> _titles = [
    'نظرة عامة',
    'الحسابات',
    'القيود اليومية',
    'الصناديق',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الوحدة المالية - ${_titles[_selectedIndex]}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'نظرة عامة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_tree),
            label: 'الحسابات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'القيود',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'الصناديق',
          ),
        ],
      ),
    );
  }
}

/// تبويب النظرة العامة للوحدة المالية
/// Finance Overview Tab
class FinanceOverviewTab extends StatelessWidget {
  const FinanceOverviewTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الملخص
          const Text(
            'ملخص مالي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // صف البطاقات الأول
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي الأصول',
                  value: '0.00 ر.س',
                  icon: Icons.account_balance_wallet,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي الخصوم',
                  value: '0.00 ر.س',
                  icon: Icons.credit_card,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // صف البطاقات الثاني
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي الصناديق',
                  value: '0.00 ر.س',
                  icon: Icons.account_balance,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSummaryCard(
                  title: 'القيود المعلقة',
                  value: '0',
                  icon: Icons.pending_actions,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // الإجراءات السريعة
          const Text(
            'الإجراءات السريعة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1.5,
            children: [
              _buildActionCard(
                title: 'إنشاء قيد يومي',
                icon: Icons.add_circle,
                color: Colors.green,
                onTap: () {
                  // TODO: فتح حوار إنشاء قيد
                },
              ),
              _buildActionCard(
                title: 'إضافة حساب',
                icon: Icons.account_tree,
                color: Colors.blue,
                onTap: () {
                  // TODO: فتح حوار إنشاء حساب
                },
              ),
              _buildActionCard(
                title: 'إضافة صندوق',
                icon: Icons.account_balance_wallet,
                color: Colors.purple,
                onTap: () {
                  // TODO: فتح حوار إنشاء صندوق
                },
              ),
              _buildActionCard(
                title: 'تقرير ميزان المراجعة',
                icon: Icons.assessment,
                color: Colors.teal,
                onTap: () {
                  // TODO: فتح تقرير ميزان المراجعة
                },
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // آخر العمليات
          const Text(
            'آخر العمليات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildRecentTransactionItem(
                    title: 'قيد يومي JE202400001',
                    subtitle: 'تم الإنشاء اليوم',
                    amount: '1,500.00 ر.س',
                    icon: Icons.receipt_long,
                    color: Colors.green,
                  ),
                  const Divider(),
                  _buildRecentTransactionItem(
                    title: 'إنشاء حساب جديد',
                    subtitle: 'النقدية في الصندوق',
                    amount: '',
                    icon: Icons.account_tree,
                    color: Colors.blue,
                  ),
                  const Divider(),
                  _buildRecentTransactionItem(
                    title: 'تحويل بين الصناديق',
                    subtitle: 'من الصندوق الرئيسي إلى البنك',
                    amount: '5,000.00 ر.س',
                    icon: Icons.swap_horiz,
                    color: Colors.orange,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    Icons.trending_up,
                    color: color,
                    size: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentTransactionItem({
    required String title,
    required String subtitle,
    required String amount,
    required IconData icon,
    required Color color,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(subtitle),
      trailing: amount.isNotEmpty
          ? Text(
              amount,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            )
          : null,
      contentPadding: EdgeInsets.zero,
    );
  }
}
