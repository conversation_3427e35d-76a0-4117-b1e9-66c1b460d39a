import 'package:flutter/material.dart';
import '../../models/finance/journal_entry.dart';

/// ويدجت بطاقة القيد اليومي
/// Journal Entry Card Widget
class JournalEntryCard extends StatelessWidget {
  final JournalEntry journalEntry;
  final VoidCallback? onTap;
  final VoidCallback? onPost;
  final VoidCallback? onCancel;

  const JournalEntryCard({
    Key? key,
    required this.journalEntry,
    this.onTap,
    this.onPost,
    this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // الصف الأول: معلومات القيد الأساسية
              Row(
                children: [
                  // أيقونة حالة القيد
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getStatusIcon(),
                      color: _getStatusColor(),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات القيد
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // رقم القيد والتاريخ
                        Row(
                          children: [
                            Text(
                              journalEntry.entryNumber,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor().withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                journalEntry.statusText,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: _getStatusColor(),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        // التاريخ والوصف
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${journalEntry.entryDate.day}/${journalEntry.entryDate.month}/${journalEntry.entryDate.year}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            if (journalEntry.description != null) ...[
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  journalEntry.description!,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  // المبالغ
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // إجمالي المدين
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'مدين: ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '${journalEntry.totalDebit.toStringAsFixed(2)} ر.س',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.red[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      // إجمالي الدائن
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'دائن: ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '${journalEntry.totalCredit.toStringAsFixed(2)} ر.س',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              
              // الصف الثاني: معلومات إضافية
              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 8),
              
              Row(
                children: [
                  // معلومات المرجع
                  if (journalEntry.referenceNumber != null)
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.link,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'مرجع: ${journalEntry.referenceNumber}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    const Expanded(child: SizedBox()),
                  
                  // مؤشر التوازن
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        journalEntry.isBalanced ? Icons.check_circle : Icons.warning,
                        size: 16,
                        color: journalEntry.isBalanced ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        journalEntry.isBalanced ? 'متوازن' : 'غير متوازن',
                        style: TextStyle(
                          fontSize: 12,
                          color: journalEntry.isBalanced ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  
                  // عدد الخطوط
                  const SizedBox(width: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${journalEntry.lines.length} خط',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
              
              // الصف الثالث: أزرار الإجراءات (للقيود المسودة)
              if (journalEntry.isDraft || !journalEntry.isCancelled) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    // زر عرض التفاصيل
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onTap,
                        icon: const Icon(Icons.visibility, size: 16),
                        label: const Text('التفاصيل'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    
                    // أزرار الإجراءات حسب الحالة
                    if (journalEntry.isDraft) ...[
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: onPost,
                          icon: const Icon(Icons.publish, size: 16),
                          label: const Text('ترحيل'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                    
                    if (!journalEntry.isCancelled) ...[
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel, size: 20),
                        color: Colors.red,
                        tooltip: 'إلغاء القيد',
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.red[50],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (journalEntry.status) {
      case 'draft':
        return Colors.orange;
      case 'posted':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (journalEntry.status) {
      case 'draft':
        return Icons.edit_note;
      case 'posted':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }
}
