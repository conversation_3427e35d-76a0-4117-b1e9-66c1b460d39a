using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج الأصل
    /// Asset Model
    /// </summary>
    [Table("assets")]
    public class Asset
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("asset_code")]
        [StringLength(50)]
        public string AssetCode { get; set; } = string.Empty;

        [Required]
        [Column("asset_name")]
        [StringLength(200)]
        public string AssetName { get; set; } = string.Empty;

        [Column("description")]
        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        [Column("asset_type")]
        [StringLength(50)]
        public string AssetType { get; set; } = string.Empty; // fixed, current, intangible

        [Required]
        [Column("asset_category")]
        [StringLength(100)]
        public string AssetCategory { get; set; } = string.Empty;

        [Column("asset_subcategory")]
        [StringLength(100)]
        public string? AssetSubcategory { get; set; }

        [Required]
        [Column("account_id")]
        public int AccountId { get; set; }

        [Column("accumulated_depreciation_account_id")]
        public int? AccumulatedDepreciationAccountId { get; set; }

        [Column("depreciation_expense_account_id")]
        public int? DepreciationExpenseAccountId { get; set; }

        [Required]
        [Column("purchase_date")]
        public DateTime PurchaseDate { get; set; }

        [Required]
        [Column("purchase_cost", TypeName = "decimal(18,2)")]
        public decimal PurchaseCost { get; set; }

        [Column("current_value", TypeName = "decimal(18,2)")]
        public decimal CurrentValue { get; set; }

        [Column("salvage_value", TypeName = "decimal(18,2)")]
        public decimal SalvageValue { get; set; } = 0;

        [Column("useful_life_years")]
        public int? UsefulLifeYears { get; set; }

        [Column("useful_life_units")]
        public int? UsefulLifeUnits { get; set; }

        [Column("depreciation_method")]
        [StringLength(50)]
        public string? DepreciationMethod { get; set; } // straight_line, declining_balance, units_of_production

        [Column("depreciation_rate", TypeName = "decimal(5,2)")]
        public decimal? DepreciationRate { get; set; }

        [Column("accumulated_depreciation", TypeName = "decimal(18,2)")]
        public decimal AccumulatedDepreciation { get; set; } = 0;

        [Column("location_id")]
        public int? LocationId { get; set; }

        [Column("department_id")]
        public int? DepartmentId { get; set; }

        [Column("responsible_user_id")]
        public int? ResponsibleUserId { get; set; }

        [Column("supplier_id")]
        public int? SupplierId { get; set; }

        [Column("serial_number")]
        [StringLength(100)]
        public string? SerialNumber { get; set; }

        [Column("model")]
        [StringLength(100)]
        public string? Model { get; set; }

        [Column("manufacturer")]
        [StringLength(100)]
        public string? Manufacturer { get; set; }

        [Column("warranty_start_date")]
        public DateTime? WarrantyStartDate { get; set; }

        [Column("warranty_end_date")]
        public DateTime? WarrantyEndDate { get; set; }

        [Column("insurance_policy_number")]
        [StringLength(100)]
        public string? InsurancePolicyNumber { get; set; }

        [Column("insurance_value", TypeName = "decimal(18,2)")]
        public decimal? InsuranceValue { get; set; }

        [Column("insurance_expiry_date")]
        public DateTime? InsuranceExpiryDate { get; set; }

        [Column("status")]
        [StringLength(50)]
        public string Status { get; set; } = "active"; // active, disposed, sold, stolen, damaged

        [Column("condition_status")]
        [StringLength(50)]
        public string? ConditionStatus { get; set; } // excellent, good, fair, poor

        [Column("disposal_date")]
        public DateTime? DisposalDate { get; set; }

        [Column("disposal_value", TypeName = "decimal(18,2)")]
        public decimal? DisposalValue { get; set; }

        [Column("disposal_method")]
        [StringLength(50)]
        public string? DisposalMethod { get; set; } // sale, donation, scrap, trade_in

        [Column("notes")]
        [StringLength(2000)]
        public string? Notes { get; set; }

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AccountId")]
        public virtual Account? Account { get; set; }

        [ForeignKey("AccumulatedDepreciationAccountId")]
        public virtual Account? AccumulatedDepreciationAccount { get; set; }

        [ForeignKey("DepreciationExpenseAccountId")]
        public virtual Account? DepreciationExpenseAccount { get; set; }

        // Location navigation property removed - Location model not available

        [ForeignKey("DepartmentId")]
        public virtual Department? Department { get; set; }

        [ForeignKey("ResponsibleUserId")]
        public virtual User? ResponsibleUser { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User? Creator { get; set; }

        [ForeignKey("UpdatedBy")]
        public virtual User? Updater { get; set; }

        // Collections
        public virtual ICollection<AssetDepreciation> Depreciations { get; set; } = new List<AssetDepreciation>();
        public virtual ICollection<AssetMaintenance> MaintenanceRecords { get; set; } = new List<AssetMaintenance>();
        public virtual ICollection<AssetValuation> Valuations { get; set; } = new List<AssetValuation>();
        public virtual ICollection<AssetTransfer> Transfers { get; set; } = new List<AssetTransfer>();

        // Calculated Properties
        [NotMapped]
        public decimal BookValue => CurrentValue - AccumulatedDepreciation;

        [NotMapped]
        public bool IsDepreciable => AssetType == "fixed" && UsefulLifeYears.HasValue && UsefulLifeYears > 0;

        [NotMapped]
        public bool IsWarrantyValid => WarrantyEndDate.HasValue && WarrantyEndDate > DateTime.Now;

        [NotMapped]
        public bool IsInsuranceValid => InsuranceExpiryDate.HasValue && InsuranceExpiryDate > DateTime.Now;

        [NotMapped]
        public string DisplayName => $"{AssetCode} - {AssetName}";

        [NotMapped]
        public int AgeInYears => DateTime.Now.Year - PurchaseDate.Year;

        [NotMapped]
        public decimal DepreciationPercentage => PurchaseCost > 0 ? (AccumulatedDepreciation / PurchaseCost) * 100 : 0;
    }

    /// <summary>
    /// نموذج استهلاك الأصل
    /// Asset Depreciation Model
    /// </summary>
    [Table("asset_depreciations")]
    public class AssetDepreciation
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("asset_id")]
        public int AssetId { get; set; }

        [Required]
        [Column("depreciation_date")]
        public DateTime DepreciationDate { get; set; }

        [Required]
        [Column("depreciation_amount", TypeName = "decimal(18,2)")]
        public decimal DepreciationAmount { get; set; }

        [Required]
        [Column("accumulated_depreciation", TypeName = "decimal(18,2)")]
        public decimal AccumulatedDepreciation { get; set; }

        [Required]
        [Column("book_value", TypeName = "decimal(18,2)")]
        public decimal BookValue { get; set; }

        [Column("journal_entry_id")]
        public int? JournalEntryId { get; set; }

        [Column("depreciation_method")]
        [StringLength(50)]
        public string? DepreciationMethod { get; set; }

        [Column("calculation_basis")]
        [StringLength(100)]
        public string? CalculationBasis { get; set; }

        [Required]
        [Column("period_year")]
        public int PeriodYear { get; set; }

        [Required]
        [Column("period_month")]
        public int PeriodMonth { get; set; }

        [Column("is_manual")]
        public bool IsManual { get; set; } = false;

        [Column("notes")]
        [StringLength(500)]
        public string? Notes { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Column("created_at")]
        public long CreatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AssetId")]
        public virtual Asset? Asset { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User? Creator { get; set; }
    }

    /// <summary>
    /// نموذج صيانة الأصل
    /// Asset Maintenance Model
    /// </summary>
    [Table("asset_maintenances")]
    public class AssetMaintenance
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("asset_id")]
        public int AssetId { get; set; }

        [Required]
        [Column("maintenance_type")]
        [StringLength(50)]
        public string MaintenanceType { get; set; } = string.Empty; // preventive, corrective, emergency

        [Required]
        [Column("maintenance_date")]
        public DateTime MaintenanceDate { get; set; }

        [Column("scheduled_date")]
        public DateTime? ScheduledDate { get; set; }

        [Column("completed_date")]
        public DateTime? CompletedDate { get; set; }

        [Required]
        [Column("description")]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Column("cost", TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }

        [Column("vendor_id")]
        public int? VendorId { get; set; }

        [Column("technician_id")]
        public int? TechnicianId { get; set; }

        [Column("status")]
        [StringLength(50)]
        public string Status { get; set; } = "scheduled"; // scheduled, in_progress, completed, cancelled

        [Column("priority")]
        [StringLength(20)]
        public string Priority { get; set; } = "medium"; // low, medium, high, critical

        [Column("downtime_hours")]
        public decimal? DowntimeHours { get; set; }

        [Column("parts_used")]
        [StringLength(2000)]
        public string? PartsUsed { get; set; }

        [Column("work_performed")]
        [StringLength(2000)]
        public string? WorkPerformed { get; set; }

        [Column("next_maintenance_date")]
        public DateTime? NextMaintenanceDate { get; set; }

        [Column("warranty_work")]
        public bool IsWarrantyWork { get; set; } = false;

        [Column("notes")]
        [StringLength(2000)]
        public string? Notes { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AssetId")]
        public virtual Asset? Asset { get; set; }

        [ForeignKey("TechnicianId")]
        public virtual User? Technician { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User? Creator { get; set; }

        [ForeignKey("UpdatedBy")]
        public virtual User? Updater { get; set; }
    }

    /// <summary>
    /// نموذج تقييم الأصل
    /// Asset Valuation Model
    /// </summary>
    [Table("asset_valuations")]
    public class AssetValuation
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("asset_id")]
        public int AssetId { get; set; }

        [Required]
        [Column("valuation_date")]
        public DateTime ValuationDate { get; set; }

        [Required]
        [Column("valuation_method")]
        [StringLength(50)]
        public string ValuationMethod { get; set; } = string.Empty; // market, replacement, income, book

        [Required]
        [Column("valuation_amount", TypeName = "decimal(18,2)")]
        public decimal ValuationAmount { get; set; }

        [Column("previous_value", TypeName = "decimal(18,2)")]
        public decimal? PreviousValue { get; set; }

        [Column("valuer_name")]
        [StringLength(200)]
        public string? ValuerName { get; set; }

        [Column("valuer_license")]
        [StringLength(100)]
        public string? ValuerLicense { get; set; }

        [Column("valuation_report")]
        [StringLength(500)]
        public string? ValuationReport { get; set; }

        [Column("reason")]
        [StringLength(200)]
        public string? Reason { get; set; }

        [Column("notes")]
        [StringLength(1000)]
        public string? Notes { get; set; }

        [Column("is_official")]
        public bool IsOfficial { get; set; } = false;

        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Column("created_at")]
        public long CreatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AssetId")]
        public virtual Asset? Asset { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User? Creator { get; set; }

        // Calculated Properties
        [NotMapped]
        public decimal? ValueChange => PreviousValue.HasValue ? ValuationAmount - PreviousValue.Value : null;

        [NotMapped]
        public decimal? ValueChangePercentage => PreviousValue.HasValue && PreviousValue > 0 
            ? ((ValuationAmount - PreviousValue.Value) / PreviousValue.Value) * 100 
            : null;
    }

    /// <summary>
    /// نموذج نقل الأصل
    /// Asset Transfer Model
    /// </summary>
    [Table("asset_transfers")]
    public class AssetTransfer
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("asset_id")]
        public int AssetId { get; set; }

        [Required]
        [Column("transfer_date")]
        public DateTime TransferDate { get; set; }

        [Column("from_location_id")]
        public int? FromLocationId { get; set; }

        [Column("to_location_id")]
        public int? ToLocationId { get; set; }

        [Column("from_department_id")]
        public int? FromDepartmentId { get; set; }

        [Column("to_department_id")]
        public int? ToDepartmentId { get; set; }

        [Column("from_responsible_user_id")]
        public int? FromResponsibleUserId { get; set; }

        [Column("to_responsible_user_id")]
        public int? ToResponsibleUserId { get; set; }

        [Required]
        [Column("reason")]
        [StringLength(500)]
        public string Reason { get; set; } = string.Empty;

        [Column("condition_before")]
        [StringLength(200)]
        public string? ConditionBefore { get; set; }

        [Column("condition_after")]
        [StringLength(200)]
        public string? ConditionAfter { get; set; }

        [Column("approved_by")]
        public int? ApprovedBy { get; set; }

        [Column("approved_at")]
        public long? ApprovedAt { get; set; }

        [Column("notes")]
        [StringLength(1000)]
        public string? Notes { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Column("created_at")]
        public long CreatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AssetId")]
        public virtual Asset? Asset { get; set; }

        // Location navigation properties removed - Location model not available

        [ForeignKey("FromDepartmentId")]
        public virtual Department? FromDepartment { get; set; }

        [ForeignKey("ToDepartmentId")]
        public virtual Department? ToDepartment { get; set; }

        [ForeignKey("FromResponsibleUserId")]
        public virtual User? FromResponsibleUser { get; set; }

        [ForeignKey("ToResponsibleUserId")]
        public virtual User? ToResponsibleUser { get; set; }

        [ForeignKey("ApprovedBy")]
        public virtual User? Approver { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User? Creator { get; set; }
    }
}
