using webApi.Models.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// واجهة خدمة المحاسبة الأساسية
    /// Accounting Service Interface
    /// </summary>
    public interface IAccountingService
    {
        // ===================================================================
        // إدارة الحسابات (Chart of Accounts)
        // ===================================================================

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        Task<Account> CreateAccountAsync(CreateAccountRequest request);

        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        Task<List<Account>> GetAllAccountsAsync();

        /// <summary>
        /// الحصول على الحسابات الهرمية
        /// </summary>
        Task<List<Account>> GetAccountsHierarchyAsync();

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        Task<Account?> GetAccountByIdAsync(int accountId);

        /// <summary>
        /// الحصول على رصيد الحساب
        /// </summary>
        Task<decimal> GetAccountBalanceAsync(int accountId, DateTime? asOfDate = null);

        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        Task<bool> UpdateAccountBalanceAsync(int accountId, decimal amount, bool isDebit);

        // ===================================================================
        // إدارة القيود اليومية (Journal Entries)
        // ===================================================================

        /// <summary>
        /// إنشاء قيد يومي
        /// </summary>
        Task<JournalEntry> CreateJournalEntryAsync(CreateJournalEntryRequest request);

        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        Task<bool> PostJournalEntryAsync(int journalEntryId, int postedBy);

        /// <summary>
        /// التحقق من توازن القيد
        /// </summary>
        Task<bool> ValidateJournalEntryBalanceAsync(JournalEntry entry);

        /// <summary>
        /// إلغاء قيد يومي
        /// </summary>
        Task<bool> CancelJournalEntryAsync(int journalEntryId, int cancelledBy);

        /// <summary>
        /// الحصول على القيود اليومية
        /// </summary>
        Task<List<JournalEntry>> GetJournalEntriesAsync(JournalEntryFilter filter);

        // ===================================================================
        // التقارير المحاسبية
        // ===================================================================

        /// <summary>
        /// تقرير ميزان المراجعة
        /// </summary>
        Task<TrialBalanceReport> GenerateTrialBalanceAsync(DateTime asOfDate);

        /// <summary>
        /// تقرير كشف الحساب
        /// </summary>
        Task<AccountStatementReport> GenerateAccountStatementAsync(int accountId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تقرير الأرصدة
        /// </summary>
        Task<List<AccountBalance>> GetAccountBalancesAsync(DateTime asOfDate);
    }

    // ===================================================================
    // نماذج الطلبات والاستجابات
    // ===================================================================

    public class CreateAccountRequest
    {
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public int? ParentAccountId { get; set; }
        public string BalanceType { get; set; } = string.Empty;
        public int? LinkedPersonId { get; set; }
        public string? Description { get; set; }
        public int CreatedBy { get; set; }
    }

    public class CreateJournalEntryRequest
    {
        public DateTime EntryDate { get; set; }
        public string? Description { get; set; }
        public string? ReferenceType { get; set; }
        public int? ReferenceId { get; set; }
        public string? ReferenceNumber { get; set; }
        public List<CreateJournalEntryLineRequest> Lines { get; set; } = new();
        public int CreatedBy { get; set; }
    }

    public class CreateJournalEntryLineRequest
    {
        public int AccountId { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public string? Description { get; set; }
        public int? CostCenterId { get; set; }
        public int? ProjectId { get; set; }
    }

    public class JournalEntryFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Status { get; set; }
        public string? ReferenceType { get; set; }
        public int? AccountId { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    public class TrialBalanceReport
    {
        public DateTime AsOfDate { get; set; }
        public List<TrialBalanceItem> Items { get; set; } = new();
        public decimal TotalDebits { get; set; }
        public decimal TotalCredits { get; set; }
    }

    public class TrialBalanceItem
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
    }

    public class AccountStatementReport
    {
        public Account Account { get; set; } = null!;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<AccountStatementItem> Transactions { get; set; } = new();
    }

    public class AccountStatementItem
    {
        public DateTime Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public string ReferenceNumber { get; set; } = string.Empty;
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal Balance { get; set; }
    }

    public class AccountBalance
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public DateTime AsOfDate { get; set; }
    }
}
