using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج الحساب المحاسبي
    /// Chart of Accounts Model
    /// </summary>
    [Table("chart_of_accounts")]
    public class Account
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        [Column("account_code")]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Column("account_name")]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column("account_type")]
        public string AccountType { get; set; } = string.Empty; // Asset, Liability, Equity, Revenue, Expense

        [Column("parent_account_id")]
        public int? ParentAccountId { get; set; }

        [Column("account_level")]
        public int AccountLevel { get; set; } = 1;

        [Required]
        [StringLength(10)]
        [Column("balance_type")]
        public string BalanceType { get; set; } = string.Empty; // Debit, Credit

        [Column("current_balance", TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("is_system_account")]
        public bool IsSystemAccount { get; set; } = false;

        [Column("linked_person_id")]
        public int? LinkedPersonId { get; set; }

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ParentAccountId")]
        public virtual Account? ParentAccount { get; set; }

        public virtual ICollection<Account> ChildAccounts { get; set; } = new List<Account>();

        [ForeignKey("LinkedPersonId")]
        public virtual User? LinkedPerson { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<JournalEntryLine> JournalEntryLines { get; set; } = new List<JournalEntryLine>();

        public virtual ICollection<Fund> Funds { get; set; } = new List<Fund>();

        public virtual ICollection<BudgetLine> BudgetLines { get; set; } = new List<BudgetLine>();

        // Calculated Properties
        [NotMapped]
        public bool HasChildren => ChildAccounts.Any();

        [NotMapped]
        public bool IsPersonalAccount => LinkedPersonId.HasValue;

        [NotMapped]
        public string FullAccountCode => ParentAccount != null 
            ? $"{ParentAccount.FullAccountCode}.{AccountCode}" 
            : AccountCode;

        [NotMapped]
        public string DisplayName => $"{AccountCode} - {AccountName}";
    }
}
