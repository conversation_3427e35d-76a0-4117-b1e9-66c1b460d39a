using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج حركة الصندوق
    /// Fund Transaction Model
    /// </summary>
    [Table("fund_transactions")]
    public class FundTransaction
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("fund_id")]
        public int FundId { get; set; }

        [Required]
        [StringLength(20)]
        [Column("transaction_type")]
        public string TransactionType { get; set; } = string.Empty; // deposit, withdrawal, transfer_in, transfer_out

        [Required]
        [Column("amount", TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Column("balance_after", TypeName = "decimal(18,2)")]
        public decimal BalanceAfter { get; set; }

        [StringLength(100)]
        [Column("reference_number")]
        public string? ReferenceNumber { get; set; }

        [StringLength(50)]
        [Column("reference_type")]
        public string? ReferenceType { get; set; } // payroll, expense, transfer, manual

        [Column("reference_id")]
        public int? ReferenceId { get; set; }

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Column("journal_entry_id")]
        public int? JournalEntryId { get; set; }

        [Required]
        [Column("transaction_date")]
        public DateTime TransactionDate { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("FundId")]
        public virtual Fund Fund { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        // Calculated Properties
        [NotMapped]
        public bool IsDeposit => TransactionType == "deposit" || TransactionType == "transfer_in";

        [NotMapped]
        public bool IsWithdrawal => TransactionType == "withdrawal" || TransactionType == "transfer_out";

        [NotMapped]
        public string TransactionTypeText => TransactionType switch
        {
            "deposit" => "إيداع",
            "withdrawal" => "سحب",
            "transfer_in" => "تحويل وارد",
            "transfer_out" => "تحويل صادر",
            _ => "غير محدد"
        };

        [NotMapped]
        public string AmountDisplay => IsDeposit ? $"+{Amount:N2}" : $"-{Amount:N2}";
    }
}
