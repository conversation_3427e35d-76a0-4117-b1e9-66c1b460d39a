using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج قالب الصرف
    /// Expense Template Model
    /// </summary>
    [Table("expense_templates")]
    public class ExpenseTemplate
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Column("name_arabic")]
        public string NameArabic { get; set; } = string.Empty;

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("expense_item_type_id")]
        public int ExpenseItemTypeId { get; set; }

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Required]
        [Column("is_default")]
        public bool IsDefault { get; set; } = false;

        [Column("template_config", TypeName = "nvarchar(max)")]
        public string? TemplateConfig { get; set; } // JSON configuration

        [Column("approval_levels", TypeName = "nvarchar(max)")]
        public string? ApprovalLevels { get; set; } // JSON approval configuration

        [Column("calculation_rules", TypeName = "nvarchar(max)")]
        public string? CalculationRules { get; set; } // JSON calculation rules

        [Column("default_lines", TypeName = "nvarchar(max)")]
        public string? DefaultLines { get; set; } // JSON default expense lines

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ExpenseItemTypeId")]
        public virtual ExpenseItemType ExpenseItemType { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("UpdatedBy")]
        public virtual User? Updater { get; set; }

        public virtual ICollection<Expense> Expenses { get; set; } = new List<Expense>();

        // Calculated Properties
        [NotMapped]
        public ExpenseTemplateConfig? ParsedTemplateConfig
        {
            get
            {
                if (string.IsNullOrEmpty(TemplateConfig))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<ExpenseTemplateConfig>(TemplateConfig);
                }
                catch
                {
                    return null;
                }
            }
        }

        [NotMapped]
        public List<ExpenseApprovalLevel>? ParsedApprovalLevels
        {
            get
            {
                if (string.IsNullOrEmpty(ApprovalLevels))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<ExpenseApprovalLevel>>(ApprovalLevels);
                }
                catch
                {
                    return null;
                }
            }
        }

        [NotMapped]
        public List<ExpenseCalculationRule>? ParsedCalculationRules
        {
            get
            {
                if (string.IsNullOrEmpty(CalculationRules))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<ExpenseCalculationRule>>(CalculationRules);
                }
                catch
                {
                    return null;
                }
            }
        }

        [NotMapped]
        public List<ExpenseLineTemplate>? ParsedDefaultLines
        {
            get
            {
                if (string.IsNullOrEmpty(DefaultLines))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<ExpenseLineTemplate>>(DefaultLines);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// تحديد إعدادات القالب
        /// </summary>
        public void SetTemplateConfig(ExpenseTemplateConfig config)
        {
            TemplateConfig = System.Text.Json.JsonSerializer.Serialize(config);
        }

        /// <summary>
        /// تحديد مستويات الاعتماد
        /// </summary>
        public void SetApprovalLevels(List<ExpenseApprovalLevel> levels)
        {
            ApprovalLevels = System.Text.Json.JsonSerializer.Serialize(levels);
        }

        /// <summary>
        /// تحديد قواعد الحساب
        /// </summary>
        public void SetCalculationRules(List<ExpenseCalculationRule> rules)
        {
            CalculationRules = System.Text.Json.JsonSerializer.Serialize(rules);
        }

        /// <summary>
        /// تحديد الخطوط الافتراضية
        /// </summary>
        public void SetDefaultLines(List<ExpenseLineTemplate> lines)
        {
            DefaultLines = System.Text.Json.JsonSerializer.Serialize(lines);
        }
    }

    /// <summary>
    /// إعدادات قالب الصرف
    /// </summary>
    public class ExpenseTemplateConfig
    {
        public bool AllowMultipleLines { get; set; } = true;
        public bool RequireAttachments { get; set; } = false;
        public int MinAttachments { get; set; } = 0;
        public int MaxAttachments { get; set; } = 10;
        public List<string> AllowedFileTypes { get; set; } = new();
        public decimal? MaxFileSize { get; set; } // in MB
        public bool AutoCalculateTotal { get; set; } = true;
        public bool AllowManualTotal { get; set; } = false;
        public string? TotalCalculationFormula { get; set; }
    }

    /// <summary>
    /// مستوى اعتماد الصرف
    /// </summary>
    public class ExpenseApprovalLevel
    {
        public int Level { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public string ApprovalType { get; set; } = "user"; // user, role, department
        public List<int> ApproverIds { get; set; } = new();
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public bool IsRequired { get; set; } = true;
        public int TimeoutHours { get; set; } = 24;
    }

    /// <summary>
    /// قاعدة حساب الصرف
    /// </summary>
    public class ExpenseCalculationRule
    {
        public string RuleName { get; set; } = string.Empty;
        public string RuleType { get; set; } = "fixed"; // fixed, percentage, formula
        public string TargetField { get; set; } = string.Empty;
        public decimal? FixedValue { get; set; }
        public decimal? PercentageValue { get; set; }
        public string? Formula { get; set; }
        public string? Condition { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// قالب خط الصرف
    /// </summary>
    public class ExpenseLineTemplate
    {
        public string Description { get; set; } = string.Empty;
        public int? AccountId { get; set; }
        public decimal? Amount { get; set; }
        public string? AmountType { get; set; } = "fixed"; // fixed, percentage, calculated
        public string? CalculationFormula { get; set; }
        public bool IsRequired { get; set; } = true;
        public int SortOrder { get; set; } = 0;
    }
}
