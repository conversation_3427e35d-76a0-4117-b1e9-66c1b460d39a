using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج اعتماد المصروف
    /// Expense Approval Model
    /// </summary>
    [Table("expense_approvals")]
    public class ExpenseApproval
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("expense_id")]
        public int ExpenseId { get; set; }

        [Required]
        [Column("level")]
        public int Level { get; set; }

        [Required]
        [StringLength(100)]
        [Column("level_name")]
        public string LevelName { get; set; } = string.Empty;

        [Required]
        [Column("approver_id")]
        public int ApproverId { get; set; }

        [Required]
        [StringLength(20)]
        [Column("status")]
        public string Status { get; set; } = "pending"; // pending, approved, rejected, delegated

        [StringLength(500)]
        [Column("comments")]
        public string? Comments { get; set; }

        [Column("approved_at")]
        public long? ApprovedAt { get; set; }

        [Column("rejected_at")]
        public long? RejectedAt { get; set; }

        [Column("delegated_to")]
        public int? DelegatedTo { get; set; }

        [Column("delegated_at")]
        public long? DelegatedAt { get; set; }

        [StringLength(500)]
        [Column("delegation_reason")]
        public string? DelegationReason { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ExpenseId")]
        public virtual Expense Expense { get; set; } = null!;

        [ForeignKey("ApproverId")]
        public virtual User Approver { get; set; } = null!;

        [ForeignKey("DelegatedTo")]
        public virtual User? DelegatedToUser { get; set; }

        // Calculated Properties
        [NotMapped]
        public bool IsPending => Status == "pending";

        [NotMapped]
        public bool IsApproved => Status == "approved";

        [NotMapped]
        public bool IsRejected => Status == "rejected";

        [NotMapped]
        public bool IsDelegated => Status == "delegated";

        [NotMapped]
        public DateTime? ApprovedDateTime => ApprovedAt.HasValue ? 
            DateTimeOffset.FromUnixTimeSeconds(ApprovedAt.Value).DateTime : null;

        [NotMapped]
        public DateTime? RejectedDateTime => RejectedAt.HasValue ? 
            DateTimeOffset.FromUnixTimeSeconds(RejectedAt.Value).DateTime : null;

        [NotMapped]
        public DateTime? DelegatedDateTime => DelegatedAt.HasValue ? 
            DateTimeOffset.FromUnixTimeSeconds(DelegatedAt.Value).DateTime : null;

        [NotMapped]
        public DateTime CreatedDateTime => DateTimeOffset.FromUnixTimeSeconds(CreatedAt).DateTime;
    }
}
