import 'package:flutter/material.dart';

import '../../models/finance/journal_entry.dart';
import '../../services/finance/accounting_service.dart';
import 'package:flutter_application_2/services/api/api_service.dart';

import '../../widgets/finance/journal_entry_card.dart';


/// شاشة القيود اليومية
/// Journal Entries Screen
class JournalEntriesScreen extends StatefulWidget {
  const JournalEntriesScreen({Key? key}) : super(key: key);

  @override
  State<JournalEntriesScreen> createState() => _JournalEntriesScreenState();
}

class _JournalEntriesScreenState extends State<JournalEntriesScreen> {
  late AccountingService _accountingService;
  List<JournalEntry> _journalEntries = [];
  List<JournalEntry> _filteredEntries = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  String _selectedStatus = 'all';
  DateTime? _fromDate;
  DateTime? _toDate;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _accountingService = AccountingService(context.read<ApiService>());
    _loadJournalEntries();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJournalEntries() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final filter = JournalEntryFilter(
        fromDate: _fromDate,
        toDate: _toDate,
        status: _selectedStatus == 'all' ? null : _selectedStatus,
      );

      final entries = await _accountingService.getJournalEntries(filter);
      
      setState(() {
        _journalEntries = entries;
        _filteredEntries = entries;
        _isLoading = false;
      });
      
      _applyFilters();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredEntries = _journalEntries.where((entry) {
        // فلتر البحث
        bool matchesSearch = _searchQuery.isEmpty ||
            entry.entryNumber.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (entry.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

        return matchesSearch;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  void _onStatusChanged(String? status) {
    setState(() {
      _selectedStatus = status ?? 'all';
    });
    _loadJournalEntries();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _fromDate != null && _toDate != null
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
      _loadJournalEntries();
    }
  }

  void _clearDateFilter() {
    setState(() {
      _fromDate = null;
      _toDate = null;
    });
    _loadJournalEntries();
  }

  Future<void> _showCreateJournalEntryDialog() async {
    final result = await showDialog<JournalEntry>(
      context: context,
      builder: (context) => CreateJournalEntryDialog(
        accountingService: _accountingService,
      ),
    );

    if (result != null) {
      _loadJournalEntries();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء القيد "${result.entryNumber}" بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('القيود اليومية'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'تحديد فترة زمنية',
          ),
          if (_fromDate != null || _toDate != null)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearDateFilter,
              tooltip: 'مسح فلتر التاريخ',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadJournalEntries,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في القيود...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: _onSearchChanged,
                ),
                const SizedBox(height: 12),
                
                // فلاتر إضافية
                Row(
                  children: [
                    // فلتر الحالة
                    Expanded(
                      child: Row(
                        children: [
                          const Text('الحالة: ', style: TextStyle(fontWeight: FontWeight.bold)),
                          Expanded(
                            child: DropdownButton<String>(
                              value: _selectedStatus,
                              isExpanded: true,
                              items: const [
                                DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                                DropdownMenuItem(value: 'draft', child: Text('مسودة')),
                                DropdownMenuItem(value: 'posted', child: Text('مرحل')),
                                DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                              ],
                              onChanged: _onStatusChanged,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // عرض فلتر التاريخ
                    if (_fromDate != null && _toDate != null)
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'من ${_fromDate!.day}/${_fromDate!.month} إلى ${_toDate!.day}/${_toDate!.month}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          
          // المحتوى الرئيسي
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateJournalEntryDialog,
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
        tooltip: 'إضافة قيد جديد',
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل القيود اليومية...');
    }

    if (_error != null) {
      return ErrorDisplayWidget(
        error: _error!,
        onRetry: _loadJournalEntries,
      );
    }

    if (_filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _selectedStatus != 'all' || _fromDate != null
                  ? 'لا توجد قيود تطابق معايير البحث'
                  : 'لا توجد قيود يومية مسجلة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showCreateJournalEntryDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة قيد جديد'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadJournalEntries,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredEntries.length,
        itemBuilder: (context, index) {
          final entry = _filteredEntries[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: JournalEntryCard(
              journalEntry: entry,
              onTap: () => _showJournalEntryDetails(entry),
              onPost: entry.isDraft ? () => _postJournalEntry(entry) : null,
              onCancel: !entry.isCancelled ? () => _cancelJournalEntry(entry) : null,
            ),
          );
        },
      ),
    );
  }

  void _showJournalEntryDetails(JournalEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل القيد: ${entry.entryNumber}'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('رقم القيد', entry.entryNumber),
              _buildDetailRow('التاريخ', '${entry.entryDate.day}/${entry.entryDate.month}/${entry.entryDate.year}'),
              _buildDetailRow('الحالة', entry.statusText),
              _buildDetailRow('إجمالي المدين', '${entry.totalDebit.toStringAsFixed(2)} ر.س'),
              _buildDetailRow('إجمالي الدائن', '${entry.totalCredit.toStringAsFixed(2)} ر.س'),
              if (entry.description != null)
                _buildDetailRow('الوصف', entry.description!),
              if (entry.referenceNumber != null)
                _buildDetailRow('المرجع', entry.referenceNumber!),
              
              const SizedBox(height: 16),
              const Text('خطوط القيد:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              
              ...entry.lines.map((line) => Container(
                margin: const EdgeInsets.only(bottom: 4),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        line.account?.accountName ?? 'حساب غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    Text(
                      line.isDebit 
                          ? '${line.debitAmount.toStringAsFixed(2)} مدين'
                          : '${line.creditAmount.toStringAsFixed(2)} دائن',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: line.isDebit ? Colors.red : Colors.green,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (entry.isDraft)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _postJournalEntry(entry);
              },
              child: const Text('ترحيل'),
            ),
        ],
      ),
    );
  }

  Future<void> _postJournalEntry(JournalEntry entry) async {
    try {
      final success = await _accountingService.postJournalEntry(entry.id);
      if (success) {
        _loadJournalEntries();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم ترحيل القيد "${entry.entryNumber}" بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في ترحيل القيد: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _cancelJournalEntry(JournalEntry entry) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإلغاء'),
        content: Text('هل أنت متأكد من إلغاء القيد "${entry.entryNumber}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await _accountingService.cancelJournalEntry(entry.id);
        if (success) {
          _loadJournalEntries();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إلغاء القيد "${entry.entryNumber}" بنجاح'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إلغاء القيد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }
}
