using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج بنود الصرف الشجرية
    /// Expense Item Type Model with Hierarchical Structure
    /// </summary>
    [Table("expense_item_types")]
    public class ExpenseItemType
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Column("name_arabic")]
        public string NameArabic { get; set; } = string.Empty;

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        [Column("code")]
        public string Code { get; set; } = string.Empty;

        [Column("parent_id")]
        public int? ParentId { get; set; }

        [Required]
        [Column("level")]
        public int Level { get; set; } = 0;

        [Required]
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Required]
        [Column("is_leaf")]
        public bool IsLeaf { get; set; } = true;

        [Column("data_schema", TypeName = "nvarchar(max)")]
        public string? DataSchema { get; set; } // JSON schema for dynamic fields

        [StringLength(50)]
        [Column("approval_workflow")]
        public string? ApprovalWorkflow { get; set; } // simple, multi_level, department_based

        [Column("requires_budget_check")]
        public bool RequiresBudgetCheck { get; set; } = true;

        [Column("default_account_id")]
        public int? DefaultAccountId { get; set; }

        [Column("max_amount", TypeName = "decimal(18,2)")]
        public decimal? MaxAmount { get; set; }

        [Column("min_amount", TypeName = "decimal(18,2)")]
        public decimal? MinAmount { get; set; }

        [StringLength(100)]
        [Column("icon")]
        public string? Icon { get; set; }

        [StringLength(20)]
        [Column("color")]
        public string? Color { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_by")]
        public int? UpdatedBy { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ParentId")]
        public virtual ExpenseItemType? Parent { get; set; }

        public virtual ICollection<ExpenseItemType> Children { get; set; } = new List<ExpenseItemType>();

        [ForeignKey("DefaultAccountId")]
        public virtual Account? DefaultAccount { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("UpdatedBy")]
        public virtual User? Updater { get; set; }

        public virtual ICollection<ExpenseTemplate> Templates { get; set; } = new List<ExpenseTemplate>();

        public virtual ICollection<Expense> Expenses { get; set; } = new List<Expense>();

        // Calculated Properties
        [NotMapped]
        public bool HasChildren => Children.Any();

        [NotMapped]
        public string FullPath
        {
            get
            {
                var path = NameArabic;
                var current = Parent;
                while (current != null)
                {
                    path = $"{current.NameArabic} > {path}";
                    current = current.Parent;
                }
                return path;
            }
        }

        [NotMapped]
        public bool CanHaveExpenses => IsLeaf && IsActive;

        /// <summary>
        /// تحليل مخطط البيانات من JSON
        /// </summary>
        [NotMapped]
        public List<ExpenseFieldSchema>? ParsedDataSchema
        {
            get
            {
                if (string.IsNullOrEmpty(DataSchema))
                    return null;

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<ExpenseFieldSchema>>(DataSchema);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// تحديد مخطط البيانات كـ JSON
        /// </summary>
        public void SetDataSchema(List<ExpenseFieldSchema> schema)
        {
            DataSchema = System.Text.Json.JsonSerializer.Serialize(schema);
        }
    }

    /// <summary>
    /// مخطط حقل الصرف الديناميكي
    /// </summary>
    public class ExpenseFieldSchema
    {
        public string FieldName { get; set; } = string.Empty;
        public string FieldNameArabic { get; set; } = string.Empty;
        public string FieldType { get; set; } = "text"; // text, number, date, select, checkbox, file
        public bool IsRequired { get; set; } = false;
        public string? DefaultValue { get; set; }
        public List<string>? Options { get; set; } // For select fields
        public string? ValidationRule { get; set; }
        public string? Placeholder { get; set; }
        public string? HelpText { get; set; }
        public int SortOrder { get; set; } = 0;
    }
}
