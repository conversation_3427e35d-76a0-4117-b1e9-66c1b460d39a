using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services.Finance;
using webApi.Models.Finance;
using webApi.Attributes;

namespace webApi.Controllers.Finance
{
    /// <summary>
    /// متحكم إدارة الحسابات المحاسبية
    /// Accounts Controller
    /// </summary>
    [Route("api/finance/[controller]")]
    [ApiController]
    [Authorize]
    [Produces("application/json")]
    public class AccountsController : ControllerBase
    {
        private readonly IAccountingService _accountingService;
        private readonly ILogger<AccountsController> _logger;

        public AccountsController(IAccountingService accountingService, ILogger<AccountsController> logger)
        {
            _accountingService = accountingService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        [HttpGet]
        //[HasPermission("finance.accounts.view")]
        public async Task<ActionResult<List<Account>>> GetAllAccounts()
        {
            try
            {
                var accounts = await _accountingService.GetAllAccountsAsync();
                return Ok(accounts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحسابات");
                return StatusCode(500, new { message = "خطأ في الحصول على الحسابات" });
            }
        }

        /// <summary>
        /// الحصول على الهيكل الهرمي للحسابات
        /// </summary>
        [HttpGet("hierarchy")]
        //[HasPermission("finance.accounts.view")]
        public async Task<ActionResult<List<Account>>> GetAccountsHierarchy()
        {
            try
            {
                var accounts = await _accountingService.GetAccountsHierarchyAsync();
                return Ok(accounts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الهيكل الهرمي للحسابات");
                return StatusCode(500, new { message = "خطأ في الحصول على الهيكل الهرمي للحسابات" });
            }
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        [HttpGet("{id}")]
        [HasPermission("finance.accounts.view")]
        public async Task<ActionResult<Account>> GetAccountById(int id)
        {
            try
            {
                var account = await _accountingService.GetAccountByIdAsync(id);
                if (account == null)
                {
                    return NotFound(new { message = "الحساب غير موجود" });
                }

                return Ok(account);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحساب: {AccountId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على الحساب" });
            }
        }

        /// <summary>
        /// الحصول على رصيد الحساب
        /// </summary>
        [HttpGet("{id}/balance")]
        [HasPermission("finance.accounts.view")]
        public async Task<ActionResult<decimal>> GetAccountBalance(int id, [FromQuery] DateTime? asOfDate = null)
        {
            try
            {
                var balance = await _accountingService.GetAccountBalanceAsync(id, asOfDate);
                return Ok(new { accountId = id, balance, asOfDate = asOfDate ?? DateTime.Now });
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رصيد الحساب: {AccountId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على رصيد الحساب" });
            }
        }

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        [HttpPost]
        [HasPermission("finance.accounts.create")]
        public async Task<ActionResult<Account>> CreateAccount([FromBody] CreateAccountRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var account = await _accountingService.CreateAccountAsync(request);
                return CreatedAtAction(nameof(GetAccountById), new { id = account.Id }, account);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الحساب: {AccountCode}", request.AccountCode);
                return StatusCode(500, new { message = "خطأ في إنشاء الحساب" });
            }
        }

        /// <summary>
        /// تحديث حساب
        /// </summary>
        [HttpPut("{id}")]
        [HasPermission("finance.accounts.edit")]
        public async Task<ActionResult> UpdateAccount(int id, [FromBody] UpdateAccountRequest request)
        {
            try
            {
                // TODO: تنفيذ تحديث الحساب
                return Ok(new { message = "سيتم تنفيذ هذه الوظيفة لاحقاً" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الحساب: {AccountId}", id);
                return StatusCode(500, new { message = "خطأ في تحديث الحساب" });
            }
        }

        /// <summary>
        /// حذف حساب (إلغاء تفعيل)
        /// </summary>
        [HttpDelete("{id}")]
        [HasPermission("finance.accounts.delete")]
        public async Task<ActionResult> DeleteAccount(int id)
        {
            try
            {
                // TODO: تنفيذ حذف الحساب (إلغاء تفعيل)
                return Ok(new { message = "سيتم تنفيذ هذه الوظيفة لاحقاً" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الحساب: {AccountId}", id);
                return StatusCode(500, new { message = "خطأ في حذف الحساب" });
            }
        }

        /// <summary>
        /// كشف حساب
        /// </summary>
        [HttpGet("{id}/statement")]
        [HasPermission("finance.accounts.view")]
        public async Task<ActionResult<AccountStatementReport>> GetAccountStatement(
            int id, 
            [FromQuery] DateTime fromDate, 
            [FromQuery] DateTime toDate)
        {
            try
            {
                var statement = await _accountingService.GenerateAccountStatementAsync(id, fromDate, toDate);
                return Ok(statement);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج كشف الحساب: {AccountId}", id);
                return StatusCode(500, new { message = "خطأ في إنتاج كشف الحساب" });
            }
        }

        /// <summary>
        /// أرصدة جميع الحسابات
        /// </summary>
        [HttpGet("balances")]
        [HasPermission("finance.accounts.view")]
        public async Task<ActionResult<List<AccountBalance>>> GetAccountBalances([FromQuery] DateTime? asOfDate = null)
        {
            try
            {
                var balances = await _accountingService.GetAccountBalancesAsync(asOfDate ?? DateTime.Now);
                return Ok(balances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على أرصدة الحسابات");
                return StatusCode(500, new { message = "خطأ في الحصول على أرصدة الحسابات" });
            }
        }
    }

    // ===================================================================
    // نماذج الطلبات الإضافية
    // ===================================================================

    public class UpdateAccountRequest
    {
        public string AccountName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
