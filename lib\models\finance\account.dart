/// نموذج الحساب المحاسبي
/// Account Model
class Account {
  final int id;
  final String accountCode;
  final String accountName;
  final String accountType;
  final int? parentAccountId;
  final int accountLevel;
  final String balanceType;
  final double currentBalance;
  final bool isActive;
  final bool isSystemAccount;
  final int? linkedPersonId;
  final String? description;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;

  // Navigation Properties
  final Account? parentAccount;
  final List<Account> childAccounts;
  final User? linkedPerson;
  final User? creator;

  Account({
    required this.id,
    required this.accountCode,
    required this.accountName,
    required this.accountType,
    this.parentAccountId,
    required this.accountLevel,
    required this.balanceType,
    required this.currentBalance,
    required this.isActive,
    required this.isSystemAccount,
    this.linkedPersonId,
    this.description,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.parentAccount,
    this.childAccounts = const [],
    this.linked<PERSON>erson,
    this.creator,
  });

  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      id: json['id'] ?? 0,
      accountCode: json['account_code'] ?? '',
      accountName: json['account_name'] ?? '',
      accountType: json['account_type'] ?? '',
      parentAccountId: json['parent_account_id'],
      accountLevel: json['account_level'] ?? 1,
      balanceType: json['balance_type'] ?? '',
      currentBalance: (json['current_balance'] ?? 0).toDouble(),
      isActive: json['is_active'] ?? true,
      isSystemAccount: json['is_system_account'] ?? false,
      linkedPersonId: json['linked_person_id'],
      description: json['description'],
      createdBy: json['created_by'] ?? 0,
      createdAt: json['created_at'] ?? 0,
      updatedAt: json['updated_at'],
      parentAccount: json['parent_account'] != null 
          ? Account.fromJson(json['parent_account'])
          : null,
      childAccounts: json['child_accounts'] != null
          ? (json['child_accounts'] as List)
              .map((item) => Account.fromJson(item))
              .toList()
          : [],
      linkedPerson: json['linked_person'] != null
          ? User.fromJson(json['linked_person'])
          : null,
      creator: json['creator'] != null
          ? User.fromJson(json['creator'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'account_code': accountCode,
      'account_name': accountName,
      'account_type': accountType,
      'parent_account_id': parentAccountId,
      'account_level': accountLevel,
      'balance_type': balanceType,
      'current_balance': currentBalance,
      'is_active': isActive,
      'is_system_account': isSystemAccount,
      'linked_person_id': linkedPersonId,
      'description': description,
      'created_by': createdBy,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // Calculated Properties
  bool get hasChildren => childAccounts.isNotEmpty;
  bool get isPersonalAccount => linkedPersonId != null;
  
  String get fullAccountCode {
    if (parentAccount != null) {
      return '${parentAccount!.fullAccountCode}.${accountCode}';
    }
    return accountCode;
  }

  String get displayName => '$accountCode - $accountName';

  String get accountTypeText {
    switch (accountType.toLowerCase()) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق ملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      default:
        return 'غير محدد';
    }
  }

  String get balanceTypeText {
    switch (balanceType.toLowerCase()) {
      case 'debit':
        return 'مدين';
      case 'credit':
        return 'دائن';
      default:
        return 'غير محدد';
    }
  }

  Account copyWith({
    int? id,
    String? accountCode,
    String? accountName,
    String? accountType,
    int? parentAccountId,
    int? accountLevel,
    String? balanceType,
    double? currentBalance,
    bool? isActive,
    bool? isSystemAccount,
    int? linkedPersonId,
    String? description,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    Account? parentAccount,
    List<Account>? childAccounts,
    User? linkedPerson,
    User? creator,
  }) {
    return Account(
      id: id ?? this.id,
      accountCode: accountCode ?? this.accountCode,
      accountName: accountName ?? this.accountName,
      accountType: accountType ?? this.accountType,
      parentAccountId: parentAccountId ?? this.parentAccountId,
      accountLevel: accountLevel ?? this.accountLevel,
      balanceType: balanceType ?? this.balanceType,
      currentBalance: currentBalance ?? this.currentBalance,
      isActive: isActive ?? this.isActive,
      isSystemAccount: isSystemAccount ?? this.isSystemAccount,
      linkedPersonId: linkedPersonId ?? this.linkedPersonId,
      description: description ?? this.description,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parentAccount: parentAccount ?? this.parentAccount,
      childAccounts: childAccounts ?? this.childAccounts,
      linkedPerson: linkedPerson ?? this.linkedPerson,
      creator: creator ?? this.creator,
    );
  }

  @override
  String toString() {
    return 'Account{id: $id, accountCode: $accountCode, accountName: $accountName}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء حساب
/// Create Account Request Model
class CreateAccountRequest {
  final String accountCode;
  final String accountName;
  final String accountType;
  final int? parentAccountId;
  final String balanceType;
  final int? linkedPersonId;
  final String? description;

  CreateAccountRequest({
    required this.accountCode,
    required this.accountName,
    required this.accountType,
    this.parentAccountId,
    required this.balanceType,
    this.linkedPersonId,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'account_code': accountCode,
      'account_name': accountName,
      'account_type': accountType,
      'parent_account_id': parentAccountId,
      'balance_type': balanceType,
      'linked_person_id': linkedPersonId,
      'description': description,
    };
  }
}

/// نموذج رصيد الحساب
/// Account Balance Model
class AccountBalance {
  final int accountId;
  final String accountCode;
  final String accountName;
  final double balance;
  final DateTime asOfDate;

  AccountBalance({
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    required this.balance,
    required this.asOfDate,
  });

  factory AccountBalance.fromJson(Map<String, dynamic> json) {
    return AccountBalance(
      accountId: json['account_id'] ?? 0,
      accountCode: json['account_code'] ?? '',
      accountName: json['account_name'] ?? '',
      balance: (json['balance'] ?? 0).toDouble(),
      asOfDate: DateTime.parse(json['as_of_date']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'account_id': accountId,
      'account_code': accountCode,
      'account_name': accountName,
      'balance': balance,
      'as_of_date': asOfDate.toIso8601String(),
    };
  }
}

// Import required models
class User {
  final int id;
  final String name;
  final String email;

  User({
    required this.id,
    required this.name,
    required this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
    };
  }
}
