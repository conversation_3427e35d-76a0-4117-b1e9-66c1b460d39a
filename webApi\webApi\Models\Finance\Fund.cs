using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج الصندوق
    /// Fund Model
    /// </summary>
    [Table("funds")]
    public class Fund
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Column("fund_name")]
        public string FundName { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Column("fund_code")]
        public string FundCode { get; set; } = string.Empty;

        [Required]
        [Column("account_id")]
        public int AccountId { get; set; }

        [Column("location_id")]
        public int? LocationId { get; set; }

        [Column("legal_entity_id")]
        public int? LegalEntityId { get; set; }

        [Column("current_balance", TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column("opening_balance", TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Required]
        [StringLength(20)]
        [Column("fund_type")]
        public string FundType { get; set; } = "cash"; // cash, bank, petty_cash

        [StringLength(100)]
        [Column("bank_name")]
        public string? BankName { get; set; }

        [StringLength(50)]
        [Column("account_number")]
        public string? AccountNumber { get; set; }

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("is_default")]
        public bool IsDefault { get; set; } = false;

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AccountId")]
        public virtual Account Account { get; set; } = null!;

        [ForeignKey("LocationId")]
        public virtual Department? Location { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<FundTransaction> Transactions { get; set; } = new List<FundTransaction>();

        public virtual ICollection<FundingTransfer> SourceTransfers { get; set; } = new List<FundingTransfer>();

        // Calculated Properties
        [NotMapped]
        public string DisplayName => $"{FundCode} - {FundName}";

        [NotMapped]
        public string FundTypeText => FundType switch
        {
            "cash" => "نقدي",
            "bank" => "بنكي",
            "petty_cash" => "نثرية",
            _ => "غير محدد"
        };

        [NotMapped]
        public bool IsCashFund => FundType == "cash";

        [NotMapped]
        public bool IsBankFund => FundType == "bank";

        [NotMapped]
        public bool IsPettyCashFund => FundType == "petty_cash";
    }
}
