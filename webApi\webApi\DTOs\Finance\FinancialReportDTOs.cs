using System.ComponentModel.DataAnnotations;

namespace webApi.DTOs.Finance
{
    /// <summary>
    /// تقرير الميزانية العمومية
    /// </summary>
    public class BalanceSheetReport
    {
        public DateTime AsOfDate { get; set; }
        public int? DepartmentId { get; set; }
        public DateTime GeneratedAt { get; set; }

        // الأصول
        public List<AccountBalanceItem> Assets { get; set; } = new();
        public decimal TotalAssets { get; set; }

        // الخصوم
        public List<AccountBalanceItem> Liabilities { get; set; } = new();
        public decimal TotalLiabilities { get; set; }

        // حقوق الملكية
        public List<AccountBalanceItem> Equity { get; set; } = new();
        public decimal TotalEquity { get; set; }

        // التوازن
        public bool IsBalanced { get; set; }
        public decimal BalanceDifference => TotalAssets - (TotalLiabilities + TotalEquity);
    }

    /// <summary>
    /// تقرير قائمة الدخل
    /// </summary>
    public class IncomeStatementReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? DepartmentId { get; set; }
        public DateTime GeneratedAt { get; set; }

        // الإيرادات
        public List<AccountBalanceItem> Revenues { get; set; } = new();
        public decimal TotalRevenues { get; set; }

        // المصروفات
        public List<AccountBalanceItem> Expenses { get; set; } = new();
        public decimal TotalExpenses { get; set; }

        // صافي الدخل
        public decimal NetIncome { get; set; }
        public decimal GrossMargin => TotalRevenues > 0 ? (NetIncome / TotalRevenues) * 100 : 0;
    }

    /// <summary>
    /// تقرير التدفقات النقدية
    /// </summary>
    public class CashFlowReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? FundId { get; set; }
        public DateTime GeneratedAt { get; set; }

        // التدفقات الداخلة
        public List<CashFlowItem> CashInflows { get; set; } = new();
        public decimal TotalInflows { get; set; }

        // التدفقات الخارجة
        public List<CashFlowItem> CashOutflows { get; set; } = new();
        public decimal TotalOutflows { get; set; }

        // صافي التدفق النقدي
        public decimal NetCashFlow { get; set; }
    }

    /// <summary>
    /// تقرير أرصدة الصناديق
    /// </summary>
    public class FundBalanceReport
    {
        public DateTime AsOfDate { get; set; }
        public DateTime GeneratedAt { get; set; }

        public List<FundBalanceItem> FundBalances { get; set; } = new();
        public decimal TotalBalance { get; set; }
    }

    /// <summary>
    /// تقرير المصروفات
    /// </summary>
    public class ExpenseReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? DepartmentId { get; set; }
        public DateTime GeneratedAt { get; set; }

        public List<ExpenseByTypeItem> ExpensesByType { get; set; } = new();
        public List<ExpenseByDepartmentItem> ExpensesByDepartment { get; set; } = new();
        public List<ExpenseByStatusItem> ExpensesByStatus { get; set; } = new();

        public decimal TotalExpenses { get; set; }
        public int TotalCount { get; set; }
        public decimal AverageExpense { get; set; }
    }

    /// <summary>
    /// تقرير الرواتب
    /// </summary>
    public class PayrollReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? DepartmentId { get; set; }
        public DateTime GeneratedAt { get; set; }

        public List<PayrollByDepartmentItem> PayrollByDepartment { get; set; } = new();

        public int TotalEmployees { get; set; }
        public decimal TotalBasicSalary { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal TotalBonuses { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalNetSalary { get; set; }
        public decimal AverageNetSalary { get; set; }
    }

    /// <summary>
    /// تقرير الميزانية مقابل الفعلي
    /// </summary>
    public class BudgetVsActualReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? DepartmentId { get; set; }
        public DateTime GeneratedAt { get; set; }

        public List<BudgetVsActualItem> BudgetItems { get; set; } = new();
        public decimal TotalBudget { get; set; }
        public decimal TotalActual { get; set; }
        public decimal TotalVariance { get; set; }
        public decimal VariancePercentage => TotalBudget > 0 ? (TotalVariance / TotalBudget) * 100 : 0;
    }

    /// <summary>
    /// تقرير أرصدة الحسابات
    /// </summary>
    public class AccountBalanceReport
    {
        public DateTime AsOfDate { get; set; }
        public string? AccountType { get; set; }
        public DateTime GeneratedAt { get; set; }

        public List<AccountBalanceItem> AccountBalances { get; set; } = new();
        public decimal TotalBalance { get; set; }
    }

    // ===== العناصر المساعدة =====

    /// <summary>
    /// عنصر رصيد حساب
    /// </summary>
    public class AccountBalanceItem
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public decimal Balance { get; set; }
    }

    /// <summary>
    /// عنصر التدفق النقدي
    /// </summary>
    public class CashFlowItem
    {
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int TransactionCount { get; set; }
    }

    /// <summary>
    /// عنصر رصيد صندوق
    /// </summary>
    public class FundBalanceItem
    {
        public int FundId { get; set; }
        public string FundName { get; set; } = string.Empty;
        public string? FundCode { get; set; }
        public string? AccountName { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal CurrentBalance { get; set; }
        public string Currency { get; set; } = "SAR";
        public decimal BalanceChange => CurrentBalance - OpeningBalance;
    }

    /// <summary>
    /// مصروفات حسب النوع
    /// </summary>
    public class ExpenseByTypeItem
    {
        public string ExpenseType { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
    }

    /// <summary>
    /// مصروفات حسب القسم
    /// </summary>
    public class ExpenseByDepartmentItem
    {
        public string DepartmentName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
    }

    /// <summary>
    /// مصروفات حسب الحالة
    /// </summary>
    public class ExpenseByStatusItem
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
    }

    /// <summary>
    /// رواتب حسب القسم
    /// </summary>
    public class PayrollByDepartmentItem
    {
        public string DepartmentName { get; set; } = string.Empty;
        public int EmployeeCount { get; set; }
        public decimal TotalBasicSalary { get; set; }
        public decimal TotalAllowances { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalNetSalary { get; set; }
        public decimal AverageNetSalary => EmployeeCount > 0 ? TotalNetSalary / EmployeeCount : 0;
    }

    /// <summary>
    /// عنصر الميزانية مقابل الفعلي
    /// </summary>
    public class BudgetVsActualItem
    {
        public string AccountName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public decimal BudgetAmount { get; set; }
        public decimal ActualAmount { get; set; }
        public decimal Variance => ActualAmount - BudgetAmount;
        public decimal VariancePercentage => BudgetAmount > 0 ? (Variance / BudgetAmount) * 100 : 0;
        public string VarianceStatus => Variance > 0 ? "تجاوز" : Variance < 0 ? "توفير" : "متطابق";
    }

    // ===== طلبات التقارير =====

    /// <summary>
    /// طلب تقرير الميزانية العمومية
    /// </summary>
    public class BalanceSheetRequest
    {
        [Required(ErrorMessage = "تاريخ التقرير مطلوب")]
        public DateTime AsOfDate { get; set; }
        public int? DepartmentId { get; set; }
        public string Format { get; set; } = "pdf"; // pdf, excel, json
    }

    /// <summary>
    /// طلب تقرير قائمة الدخل
    /// </summary>
    public class IncomeStatementRequest
    {
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        public DateTime EndDate { get; set; }

        public int? DepartmentId { get; set; }
        public string Format { get; set; } = "pdf";
    }

    /// <summary>
    /// طلب تقرير التدفقات النقدية
    /// </summary>
    public class CashFlowRequest
    {
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        public DateTime EndDate { get; set; }

        public int? FundId { get; set; }
        public string Format { get; set; } = "pdf";
    }

    /// <summary>
    /// طلب تقرير أرصدة الصناديق
    /// </summary>
    public class FundBalanceRequest
    {
        [Required(ErrorMessage = "تاريخ التقرير مطلوب")]
        public DateTime AsOfDate { get; set; }
        public int? FundId { get; set; }
        public string Format { get; set; } = "pdf";
    }

    /// <summary>
    /// طلب تقرير المصروفات
    /// </summary>
    public class ExpenseReportRequest
    {
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        public DateTime EndDate { get; set; }

        public int? DepartmentId { get; set; }
        public string? ExpenseType { get; set; }
        public string? Status { get; set; }
        public string Format { get; set; } = "pdf";
    }

    /// <summary>
    /// طلب تقرير الرواتب
    /// </summary>
    public class PayrollReportRequest
    {
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        public DateTime EndDate { get; set; }

        public int? DepartmentId { get; set; }
        public string Format { get; set; } = "pdf";
    }

    /// <summary>
    /// طلب تقرير الميزانية مقابل الفعلي
    /// </summary>
    public class BudgetVsActualRequest
    {
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        public DateTime EndDate { get; set; }

        public int? DepartmentId { get; set; }
        public string Format { get; set; } = "pdf";
    }

    /// <summary>
    /// طلب تقرير أرصدة الحسابات
    /// </summary>
    public class AccountBalanceRequest
    {
        [Required(ErrorMessage = "تاريخ التقرير مطلوب")]
        public DateTime AsOfDate { get; set; }
        public string? AccountType { get; set; }
        public string Format { get; set; } = "pdf";
    }

    // ===== DTOs للحسابات الشخصية =====

    /// <summary>
    /// طلب إنشاء حساب شخصي
    /// </summary>
    public class CreatePersonalAccountRequest
    {
        [Required(ErrorMessage = "معرف الموظف مطلوب")]
        public int EmployeeId { get; set; }

        public string? AccountName { get; set; }
        public string? AccountType { get; set; }
        public decimal InitialBalance { get; set; } = 0;
        public string? Currency { get; set; }
        public string? Notes { get; set; }
        public int CreatedBy { get; set; }
    }

    /// <summary>
    /// طلب تحديث حساب شخصي
    /// </summary>
    public class UpdatePersonalAccountRequest
    {
        public string? AccountName { get; set; }
        public string? AccountType { get; set; }
        public string? Currency { get; set; }
        public bool? IsActive { get; set; }
        public string? Notes { get; set; }
        public int UpdatedBy { get; set; }
    }

    /// <summary>
    /// طلب إنشاء معاملة حساب شخصي
    /// </summary>
    public class CreatePersonalAccountTransactionRequest
    {
        [Required(ErrorMessage = "معرف الحساب الشخصي مطلوب")]
        public int PersonalAccountId { get; set; }

        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        public string TransactionType { get; set; } = string.Empty; // deposit, withdrawal, transfer_in, transfer_out

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "الوصف مطلوب")]
        public string Description { get; set; } = string.Empty;

        public string? ReferenceType { get; set; }
        public int? ReferenceId { get; set; }
        public string? ReferenceNumber { get; set; }
        public DateTime? TransactionDate { get; set; }
        public int CreatedBy { get; set; }
    }

    /// <summary>
    /// كشف حساب شخصي
    /// </summary>
    public class PersonalAccountStatement
    {
        public int AccountId { get; set; }
        public string AccountNumber { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedAt { get; set; }

        public decimal OpeningBalance { get; set; }
        public decimal TotalDebits { get; set; }
        public decimal TotalCredits { get; set; }
        public decimal ClosingBalance { get; set; }

        public List<PersonalAccountStatementTransaction> Transactions { get; set; } = new();
    }

    /// <summary>
    /// معاملة في كشف الحساب
    /// </summary>
    public class PersonalAccountStatementTransaction
    {
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? ReferenceNumber { get; set; }
    }

    /// <summary>
    /// ملخص حساب شخصي
    /// </summary>
    public class PersonalAccountSummary
    {
        public int AccountId { get; set; }
        public string AccountNumber { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public string Currency { get; set; } = "SAR";
        public int TransactionCount { get; set; }
        public DateTime? LastTransactionDate { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// طلب تحويل إلى حساب شخصي
    /// </summary>
    public class TransferToPersonalAccountRequest
    {
        [Required(ErrorMessage = "معرف الصندوق مطلوب")]
        public int FromFundId { get; set; }

        [Required(ErrorMessage = "معرف الحساب الشخصي مطلوب")]
        public int ToAccountId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "الوصف مطلوب")]
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// طلب تحويل من حساب شخصي
    /// </summary>
    public class TransferFromPersonalAccountRequest
    {
        [Required(ErrorMessage = "معرف الحساب الشخصي مطلوب")]
        public int FromAccountId { get; set; }

        [Required(ErrorMessage = "معرف الصندوق مطلوب")]
        public int ToFundId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "الوصف مطلوب")]
        public string Description { get; set; } = string.Empty;
    }
}
