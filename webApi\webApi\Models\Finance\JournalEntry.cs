using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج القيد اليومي
    /// Journal Entry Model
    /// </summary>
    [Table("journal_entries")]
    public class JournalEntry
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Column("entry_number")]
        public string EntryNumber { get; set; } = string.Empty;

        [Required]
        [Column("entry_date")]
        public DateTime EntryDate { get; set; }

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("total_debit", TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; }

        [Required]
        [Column("total_credit", TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; }

        [Required]
        [StringLength(20)]
        [Column("status")]
        public string Status { get; set; } = "draft"; // draft, posted, cancelled

        [StringLength(50)]
        [Column("reference_type")]
        public string? ReferenceType { get; set; } // payroll, expense, transfer, manual

        [Column("reference_id")]
        public int? ReferenceId { get; set; }

        [StringLength(100)]
        [Column("reference_number")]
        public string? ReferenceNumber { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("posted_by")]
        public int? PostedBy { get; set; }

        [Column("posted_at")]
        public long? PostedAt { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("PostedBy")]
        public virtual User? PostedByUser { get; set; }

        public virtual ICollection<JournalEntryLine> Lines { get; set; } = new List<JournalEntryLine>();

        public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();

        // Calculated Properties
        [NotMapped]
        public bool IsBalanced => Math.Abs(TotalDebit - TotalCredit) < 0.01m;

        [NotMapped]
        public bool IsDraft => Status == "draft";

        [NotMapped]
        public bool IsPosted => Status == "posted";

        [NotMapped]
        public bool IsCancelled => Status == "cancelled";

        [NotMapped]
        public decimal BalanceDifference => TotalDebit - TotalCredit;

        [NotMapped]
        public string StatusText => Status switch
        {
            "draft" => "مسودة",
            "posted" => "مرحل",
            "cancelled" => "ملغي",
            _ => "غير محدد"
        };
    }
}
