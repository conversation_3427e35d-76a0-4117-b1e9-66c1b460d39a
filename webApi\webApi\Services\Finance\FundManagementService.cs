using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// خدمة إدارة الصناديق
    /// Fund Management Service Implementation
    /// </summary>
    public class FundManagementService : IFundManagementService
    {
        private readonly TasksDbContext _context;
        private readonly IAccountingService _accountingService;
        private readonly ILogger<FundManagementService> _logger;

        public FundManagementService(
            TasksDbContext context, 
            IAccountingService accountingService,
            ILogger<FundManagementService> logger)
        {
            _context = context;
            _accountingService = accountingService;
            _logger = logger;
        }

        // ===================================================================
        // إدارة الصناديق
        // ===================================================================

        public async Task<Fund> CreateFundAsync(CreateFundRequest request)
        {
            try
            {
                // التحقق من عدم تكرار رمز الصندوق
                var existingFund = await _context.Set<Fund>()
                    .FirstOrDefaultAsync(f => f.FundCode == request.FundCode);

                if (existingFund != null)
                {
                    throw new InvalidOperationException($"رمز الصندوق {request.FundCode} موجود مسبقاً");
                }

                // التحقق من وجود الحساب المحاسبي
                var account = await _context.Set<Account>()
                    .FindAsync(request.AccountId);

                if (account == null)
                {
                    throw new ArgumentException($"الحساب المحاسبي غير موجود: {request.AccountId}");
                }

                var fund = new Fund
                {
                    FundName = request.FundName,
                    FundCode = request.FundCode,
                    AccountId = request.AccountId,
                    LocationId = request.LocationId,
                    LegalEntityId = request.LegalEntityId,
                    OpeningBalance = request.OpeningBalance,
                    CurrentBalance = request.OpeningBalance,
                    FundType = request.FundType,
                    BankName = request.BankName,
                    AccountNumber = request.AccountNumber,
                    Description = request.Description,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<Fund>().Add(fund);
                await _context.SaveChangesAsync();

                // إضافة حركة الرصيد الافتتاحي إذا كان أكبر من صفر
                if (request.OpeningBalance > 0)
                {
                    await AddFundTransactionAsync(new CreateFundTransactionRequest
                    {
                        FundId = fund.Id,
                        TransactionType = "deposit",
                        Amount = request.OpeningBalance,
                        Description = "الرصيد الافتتاحي",
                        ReferenceType = "opening_balance",
                        TransactionDate = DateTime.Now,
                        CreatedBy = request.CreatedBy
                    });
                }

                _logger.LogInformation("تم إنشاء صندوق جديد: {FundCode} - {FundName}", 
                    fund.FundCode, fund.FundName);

                return fund;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الصندوق: {FundCode}", request.FundCode);
                throw;
            }
        }

        public async Task<List<Fund>> GetAllFundsAsync()
        {
            try
            {
                return await _context.Set<Fund>()
                    .Include(f => f.Account)
                    .Include(f => f.Location)
                    .Include(f => f.Creator)
                    .Where(f => f.IsActive)
                    .OrderBy(f => f.FundCode)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الصناديق");
                throw;
            }
        }

        public async Task<Fund?> GetFundByIdAsync(int fundId)
        {
            try
            {
                return await _context.Set<Fund>()
                    .Include(f => f.Account)
                    .Include(f => f.Location)
                    .Include(f => f.Creator)
                    .FirstOrDefaultAsync(f => f.Id == fundId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الصندوق: {FundId}", fundId);
                throw;
            }
        }

        public async Task<decimal> GetFundBalanceAsync(int fundId)
        {
            try
            {
                var fund = await _context.Set<Fund>()
                    .FindAsync(fundId);

                return fund?.CurrentBalance ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رصيد الصندوق: {FundId}", fundId);
                throw;
            }
        }

        public async Task<bool> UpdateFundBalanceAsync(int fundId, decimal amount, string transactionType)
        {
            try
            {
                var fund = await _context.Set<Fund>().FindAsync(fundId);
                if (fund == null)
                {
                    return false;
                }

                if (transactionType == "deposit" || transactionType == "transfer_in")
                {
                    fund.CurrentBalance += amount;
                }
                else if (transactionType == "withdrawal" || transactionType == "transfer_out")
                {
                    fund.CurrentBalance -= amount;
                }

                fund.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث رصيد الصندوق: {FundId}", fundId);
                throw;
            }
        }

        // ===================================================================
        // حركات الصناديق
        // ===================================================================

        public async Task<FundTransaction> AddFundTransactionAsync(CreateFundTransactionRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var fund = await _context.Set<Fund>().FindAsync(request.FundId);
                if (fund == null)
                {
                    throw new ArgumentException($"الصندوق غير موجود: {request.FundId}");
                }

                // التحقق من كفاية الرصيد للسحب
                if ((request.TransactionType == "withdrawal" || request.TransactionType == "transfer_out") 
                    && fund.CurrentBalance < request.Amount)
                {
                    throw new InvalidOperationException("رصيد الصندوق غير كافي");
                }

                // تحديث رصيد الصندوق
                await UpdateFundBalanceAsync(request.FundId, request.Amount, request.TransactionType);

                // إعادة تحميل الصندوق للحصول على الرصيد المحدث
                await _context.Entry(fund).ReloadAsync();

                var fundTransaction = new FundTransaction
                {
                    FundId = request.FundId,
                    TransactionType = request.TransactionType,
                    Amount = request.Amount,
                    BalanceAfter = fund.CurrentBalance,
                    ReferenceNumber = request.ReferenceNumber,
                    ReferenceType = request.ReferenceType,
                    ReferenceId = request.ReferenceId,
                    Description = request.Description,
                    TransactionDate = request.TransactionDate,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<FundTransaction>().Add(fundTransaction);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                _logger.LogInformation("تم إضافة حركة للصندوق: {FundId} - {TransactionType} - {Amount}", 
                    request.FundId, request.TransactionType, request.Amount);

                return fundTransaction;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في إضافة حركة الصندوق: {FundId}", request.FundId);
                throw;
            }
        }

        public async Task<List<FundTransaction>> GetFundTransactionsAsync(int fundId, FundTransactionFilter filter)
        {
            try
            {
                var query = _context.Set<FundTransaction>()
                    .Include(ft => ft.Fund)
                    .Include(ft => ft.Creator)
                    .Where(ft => ft.FundId == fundId);

                if (filter.FromDate.HasValue)
                    query = query.Where(ft => ft.TransactionDate >= filter.FromDate.Value);

                if (filter.ToDate.HasValue)
                    query = query.Where(ft => ft.TransactionDate <= filter.ToDate.Value);

                if (!string.IsNullOrEmpty(filter.TransactionType))
                    query = query.Where(ft => ft.TransactionType == filter.TransactionType);

                if (!string.IsNullOrEmpty(filter.ReferenceType))
                    query = query.Where(ft => ft.ReferenceType == filter.ReferenceType);

                return await query
                    .OrderByDescending(ft => ft.TransactionDate)
                    .Skip((filter.PageNumber - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على حركات الصندوق: {FundId}", fundId);
                throw;
            }
        }

        public async Task<FundTransferResult> TransferBetweenFundsAsync(FundTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var sourceFund = await _context.Set<Fund>().FindAsync(request.SourceFundId);
                var targetFund = await _context.Set<Fund>().FindAsync(request.TargetFundId);

                if (sourceFund == null || targetFund == null)
                {
                    throw new ArgumentException("أحد الصناديق غير موجود");
                }

                if (sourceFund.CurrentBalance < request.Amount)
                {
                    throw new InvalidOperationException("رصيد الصندوق المصدر غير كافي");
                }

                // إنشاء حركة السحب من الصندوق المصدر
                var sourceTransaction = await AddFundTransactionAsync(new CreateFundTransactionRequest
                {
                    FundId = request.SourceFundId,
                    TransactionType = "transfer_out",
                    Amount = request.Amount,
                    Description = $"تحويل إلى {targetFund.FundName}",
                    ReferenceNumber = request.ReferenceNumber,
                    ReferenceType = "fund_transfer",
                    TransactionDate = DateTime.Now,
                    CreatedBy = request.CreatedBy
                });

                // إنشاء حركة الإيداع في الصندوق المستهدف
                var targetTransaction = await AddFundTransactionAsync(new CreateFundTransactionRequest
                {
                    FundId = request.TargetFundId,
                    TransactionType = "transfer_in",
                    Amount = request.Amount,
                    Description = $"تحويل من {sourceFund.FundName}",
                    ReferenceNumber = request.ReferenceNumber,
                    ReferenceType = "fund_transfer",
                    TransactionDate = DateTime.Now,
                    CreatedBy = request.CreatedBy
                });

                await transaction.CommitAsync();

                _logger.LogInformation("تم تحويل {Amount} من الصندوق {SourceFund} إلى {TargetFund}", 
                    request.Amount, sourceFund.FundName, targetFund.FundName);

                return new FundTransferResult
                {
                    Success = true,
                    Message = "تم التحويل بنجاح",
                    SourceTransaction = sourceTransaction,
                    TargetTransaction = targetTransaction
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في تحويل الأموال بين الصناديق");
                
                return new FundTransferResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        // ===================================================================
        // التحويلات التمويلية
        // ===================================================================

        public async Task<FundingTransfer> TransferToPersonalAccountAsync(FundingTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var sourceFund = await _context.Set<Fund>().FindAsync(request.SourceFundId);
                var targetAccount = await _context.Set<PersonalAccount>().FindAsync(request.TargetAccountId);

                if (sourceFund == null || targetAccount == null)
                {
                    throw new ArgumentException("الصندوق أو الحساب الشخصي غير موجود");
                }

                if (sourceFund.CurrentBalance < request.Amount)
                {
                    throw new InvalidOperationException("رصيد الصندوق غير كافي");
                }

                // توليد رقم مرجعي
                var referenceNumber = await GenerateTransferReferenceNumberAsync();

                var fundingTransfer = new FundingTransfer
                {
                    SourceFundId = request.SourceFundId,
                    TargetAccountId = request.TargetAccountId,
                    Amount = request.Amount,
                    TransferDate = request.TransferDate,
                    ReferenceNumber = referenceNumber,
                    TransferType = request.TransferType,
                    Description = request.Description,
                    Notes = request.Notes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<FundingTransfer>().Add(fundingTransfer);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                _logger.LogInformation("تم إنشاء تحويل تمويلي: {ReferenceNumber}", referenceNumber);

                return fundingTransfer;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في إنشاء التحويل التمويلي");
                throw;
            }
        }

        /// <summary>
        /// Retrieves a list of funding transfers based on the specified filter criteria
        /// </summary>
        /// <param name="filter">Filter criteria to apply to the funding transfers query</param>
        /// <returns>A list of funding transfers that match the filter criteria</returns>
        public async Task<List<FundingTransfer>> GetFundingTransfersAsync(FundingTransferFilter filter)
        {
            try
            {
                // Start with a base query that includes related entities
                var query = _context.Set<FundingTransfer>()
                    .Include(ft => ft.SourceFund)      // Include related source fund information
                    .Include(ft => ft.TargetAccount)   // Include related target account information
                    .Include(ft => ft.Creator)         // Include related creator information
                    .AsQueryable();

                // Apply date range filter if specified
                if (filter.FromDate.HasValue)
                    query = query.Where(ft => ft.TransferDate >= filter.FromDate.Value);

                // Apply end date filter if specified
                if (filter.ToDate.HasValue)
                    query = query.Where(ft => ft.TransferDate <= filter.ToDate.Value);

                // Apply status filter if specified
                if (!string.IsNullOrEmpty(filter.Status))
                    query = query.Where(ft => ft.Status == filter.Status);

                // Apply transfer type filter if specified
                if (!string.IsNullOrEmpty(filter.TransferType))
                    query = query.Where(ft => ft.TransferType == filter.TransferType);

                // Apply source fund filter if specified
                if (filter.SourceFundId.HasValue)
                    query = query.Where(ft => ft.SourceFundId == filter.SourceFundId.Value);

                // Apply target account filter if specified
                if (filter.TargetAccountId.HasValue)
                    query = query.Where(ft => ft.TargetAccountId == filter.TargetAccountId.Value);

                // Execute query with pagination and ordering
                return await query
                    .OrderByDescending(ft => ft.TransferDate)  // Order by transfer date (newest first)
                    .Skip((filter.PageNumber - 1) * filter.PageSize)  // Apply pagination offset
                    .Take(filter.PageSize)  // Apply pagination limit
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                // Log any errors that occur during the operation
                _logger.LogError(ex, "خطأ في الحصول على التحويلات التمويلية");
                throw;  // Re-throw the exception to be handled by the caller
            }
        }

        public async Task<bool> ApproveFundingTransferAsync(int transferId, int approvedBy)
        {
            try
            {
                var transfer = await _context.Set<FundingTransfer>()
                    .FindAsync(transferId);

                if (transfer == null || transfer.Status != "pending")
                {
                    return false;
                }

                transfer.ApprovedBy = approvedBy;
                transfer.ApprovedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم اعتماد التحويل التمويلي: {TransferId}", transferId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد التحويل التمويلي: {TransferId}", transferId);
                throw;
            }
        }

        public async Task<bool> CompleteFundingTransferAsync(int transferId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var transfer = await _context.Set<FundingTransfer>()
                    .Include(ft => ft.SourceFund)
                    .Include(ft => ft.TargetAccount)
                    .FirstOrDefaultAsync(ft => ft.Id == transferId);

                if (transfer == null || transfer.Status != "pending")
                {
                    return false;
                }

                // تنفيذ التحويل الفعلي
                // TODO: إضافة منطق تحديث الأرصدة وإنشاء القيود المحاسبية

                transfer.Status = "completed";
                transfer.CompletedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("تم إكمال التحويل التمويلي: {TransferId}", transferId);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في إكمال التحويل التمويلي: {TransferId}", transferId);
                throw;
            }
        }

        // ===================================================================
        // التقارير
        // ===================================================================

        public async Task<FundMovementReport> GenerateFundMovementReportAsync(int fundId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var fund = await GetFundByIdAsync(fundId);
                if (fund == null)
                {
                    throw new ArgumentException($"الصندوق غير موجود: {fundId}");
                }

                var transactions = await _context.Set<FundTransaction>()
                    .Where(ft => ft.FundId == fundId &&
                                ft.TransactionDate >= fromDate &&
                                ft.TransactionDate <= toDate)
                    .OrderBy(ft => ft.TransactionDate)
                    .ToListAsync();

                var openingBalance = await GetFundBalanceAtDateAsync(fundId, fromDate.AddDays(-1));
                var totalDeposits = transactions.Where(t => t.IsDeposit).Sum(t => t.Amount);
                var totalWithdrawals = transactions.Where(t => t.IsWithdrawal).Sum(t => t.Amount);

                return new FundMovementReport
                {
                    Fund = fund,
                    FromDate = fromDate,
                    ToDate = toDate,
                    OpeningBalance = openingBalance,
                    ClosingBalance = fund.CurrentBalance,
                    TotalDeposits = totalDeposits,
                    TotalWithdrawals = totalWithdrawals,
                    Transactions = transactions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير حركة الصندوق: {FundId}", fundId);
                throw;
            }
        }

        public async Task<List<FundBalanceReport>> GetFundBalancesReportAsync(DateTime asOfDate)
        {
            try
            {
                var funds = await _context.Set<Fund>()
                    .Where(f => f.IsActive)
                    .ToListAsync();

                var report = new List<FundBalanceReport>();

                foreach (var fund in funds)
                {
                    var balance = await GetFundBalanceAtDateAsync(fund.Id, asOfDate);
                    
                    report.Add(new FundBalanceReport
                    {
                        FundId = fund.Id,
                        FundCode = fund.FundCode,
                        FundName = fund.FundName,
                        FundType = fund.FundType,
                        CurrentBalance = balance,
                        AsOfDate = asOfDate,
                        IsActive = fund.IsActive
                    });
                }

                return report.OrderBy(r => r.FundCode).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير أرصدة الصناديق");
                throw;
            }
        }

        // ===================================================================
        // دوال مساعدة
        // ===================================================================

        private async Task<decimal> GetFundBalanceAtDateAsync(int fundId, DateTime asOfDate)
        {
            try
            {
                var fund = await _context.Set<Fund>().FindAsync(fundId);
                if (fund == null) return 0;

                var transactions = await _context.Set<FundTransaction>()
                    .Where(ft => ft.FundId == fundId && ft.TransactionDate <= asOfDate)
                    .ToListAsync();

                decimal balance = fund.OpeningBalance;
                foreach (var transaction in transactions)
                {
                    if (transaction.IsDeposit)
                        balance += transaction.Amount;
                    else
                        balance -= transaction.Amount;
                }

                return balance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب رصيد الصندوق في تاريخ محدد: {FundId}", fundId);
                return 0;
            }
        }

        private async Task<string> GenerateTransferReferenceNumberAsync()
        {
            var year = DateTime.Now.Year;
            var prefix = $"FT{year}";
            
            var lastTransfer = await _context.Set<FundingTransfer>()
                .Where(ft => ft.ReferenceNumber.StartsWith(prefix))
                .OrderByDescending(ft => ft.ReferenceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastTransfer != null)
            {
                var lastNumberStr = lastTransfer.ReferenceNumber.Substring(prefix.Length);
                if (int.TryParse(lastNumberStr, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D6}";
        }
    }
}
