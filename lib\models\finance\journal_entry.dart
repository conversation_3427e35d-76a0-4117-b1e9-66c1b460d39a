import 'account.dart';

/// نموذج القيد اليومي
/// Journal Entry Model
class JournalEntry {
  final int id;
  final String entryNumber;
  final DateTime entryDate;
  final String? description;
  final double totalDebit;
  final double totalCredit;
  final String status;
  final String? referenceType;
  final int? referenceId;
  final String? referenceNumber;
  final int createdBy;
  final int createdAt;
  final int? postedBy;
  final int? postedAt;
  final int? updatedAt;

  // Navigation Properties
  final User? creator;
  final User? postedByUser;
  final List<JournalEntryLine> lines;

  JournalEntry({
    required this.id,
    required this.entryNumber,
    required this.entryDate,
    this.description,
    required this.totalDebit,
    required this.totalCredit,
    required this.status,
    this.referenceType,
    this.referenceId,
    this.referenceNumber,
    required this.createdBy,
    required this.createdAt,
    this.postedBy,
    this.postedAt,
    this.updatedAt,
    this.creator,
    this.postedByUser,
    this.lines = const [],
  });

  factory JournalEntry.fromJson(Map<String, dynamic> json) {
    return JournalEntry(
      id: json['id'] ?? 0,
      entryNumber: json['entry_number'] ?? '',
      entryDate: DateTime.parse(json['entry_date']),
      description: json['description'],
      totalDebit: (json['total_debit'] ?? 0).toDouble(),
      totalCredit: (json['total_credit'] ?? 0).toDouble(),
      status: json['status'] ?? 'draft',
      referenceType: json['reference_type'],
      referenceId: json['reference_id'],
      referenceNumber: json['reference_number'],
      createdBy: json['created_by'] ?? 0,
      createdAt: json['created_at'] ?? 0,
      postedBy: json['posted_by'],
      postedAt: json['posted_at'],
      updatedAt: json['updated_at'],
      creator: json['creator'] != null
          ? User.fromJson(json['creator'])
          : null,
      postedByUser: json['posted_by_user'] != null
          ? User.fromJson(json['posted_by_user'])
          : null,
      lines: json['lines'] != null
          ? (json['lines'] as List)
              .map((item) => JournalEntryLine.fromJson(item))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entry_number': entryNumber,
      'entry_date': entryDate.toIso8601String(),
      'description': description,
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'status': status,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'reference_number': referenceNumber,
      'created_by': createdBy,
      'created_at': createdAt,
      'posted_by': postedBy,
      'posted_at': postedAt,
      'updated_at': updatedAt,
    };
  }

  // Calculated Properties
  bool get isBalanced => (totalDebit - totalCredit).abs() < 0.01;
  bool get isDraft => status == 'draft';
  bool get isPosted => status == 'posted';
  bool get isCancelled => status == 'cancelled';
  double get balanceDifference => totalDebit - totalCredit;

  String get statusText {
    switch (status) {
      case 'draft':
        return 'مسودة';
      case 'posted':
        return 'مرحل';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  JournalEntry copyWith({
    int? id,
    String? entryNumber,
    DateTime? entryDate,
    String? description,
    double? totalDebit,
    double? totalCredit,
    String? status,
    String? referenceType,
    int? referenceId,
    String? referenceNumber,
    int? createdBy,
    int? createdAt,
    int? postedBy,
    int? postedAt,
    int? updatedAt,
    User? creator,
    User? postedByUser,
    List<JournalEntryLine>? lines,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      entryDate: entryDate ?? this.entryDate,
      description: description ?? this.description,
      totalDebit: totalDebit ?? this.totalDebit,
      totalCredit: totalCredit ?? this.totalCredit,
      status: status ?? this.status,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      postedBy: postedBy ?? this.postedBy,
      postedAt: postedAt ?? this.postedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      creator: creator ?? this.creator,
      postedByUser: postedByUser ?? this.postedByUser,
      lines: lines ?? this.lines,
    );
  }
}

/// نموذج خط القيد اليومي
/// Journal Entry Line Model
class JournalEntryLine {
  final int id;
  final int journalEntryId;
  final int accountId;
  final double debitAmount;
  final double creditAmount;
  final String? description;
  final int? costCenterId;
  final int? projectId;
  final int lineOrder;
  final int createdAt;

  // Navigation Properties
  final Account? account;

  JournalEntryLine({
    required this.id,
    required this.journalEntryId,
    required this.accountId,
    required this.debitAmount,
    required this.creditAmount,
    this.description,
    this.costCenterId,
    this.projectId,
    required this.lineOrder,
    required this.createdAt,
    this.account,
  });

  factory JournalEntryLine.fromJson(Map<String, dynamic> json) {
    return JournalEntryLine(
      id: json['id'] ?? 0,
      journalEntryId: json['journal_entry_id'] ?? 0,
      accountId: json['account_id'] ?? 0,
      debitAmount: (json['debit_amount'] ?? 0).toDouble(),
      creditAmount: (json['credit_amount'] ?? 0).toDouble(),
      description: json['description'],
      costCenterId: json['cost_center_id'],
      projectId: json['project_id'],
      lineOrder: json['line_order'] ?? 1,
      createdAt: json['created_at'] ?? 0,
      account: json['account'] != null
          ? Account.fromJson(json['account'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'journal_entry_id': journalEntryId,
      'account_id': accountId,
      'debit_amount': debitAmount,
      'credit_amount': creditAmount,
      'description': description,
      'cost_center_id': costCenterId,
      'project_id': projectId,
      'line_order': lineOrder,
      'created_at': createdAt,
    };
  }

  // Calculated Properties
  double get amount => debitAmount > 0 ? debitAmount : creditAmount;
  String get entryType => debitAmount > 0 ? 'Debit' : 'Credit';
  String get entryTypeArabic => debitAmount > 0 ? 'مدين' : 'دائن';
  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;

  JournalEntryLine copyWith({
    int? id,
    int? journalEntryId,
    int? accountId,
    double? debitAmount,
    double? creditAmount,
    String? description,
    int? costCenterId,
    int? projectId,
    int? lineOrder,
    int? createdAt,
    Account? account,
  }) {
    return JournalEntryLine(
      id: id ?? this.id,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      accountId: accountId ?? this.accountId,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      description: description ?? this.description,
      costCenterId: costCenterId ?? this.costCenterId,
      projectId: projectId ?? this.projectId,
      lineOrder: lineOrder ?? this.lineOrder,
      createdAt: createdAt ?? this.createdAt,
      account: account ?? this.account,
    );
  }
}

/// نموذج طلب إنشاء قيد يومي
/// Create Journal Entry Request Model
class CreateJournalEntryRequest {
  final DateTime entryDate;
  final String? description;
  final String? referenceType;
  final int? referenceId;
  final String? referenceNumber;
  final List<CreateJournalEntryLineRequest> lines;

  CreateJournalEntryRequest({
    required this.entryDate,
    this.description,
    this.referenceType,
    this.referenceId,
    this.referenceNumber,
    required this.lines,
  });

  Map<String, dynamic> toJson() {
    return {
      'entry_date': entryDate.toIso8601String(),
      'description': description,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'reference_number': referenceNumber,
      'lines': lines.map((line) => line.toJson()).toList(),
    };
  }
}

/// نموذج طلب إنشاء خط قيد يومي
/// Create Journal Entry Line Request Model
class CreateJournalEntryLineRequest {
  final int accountId;
  final double debitAmount;
  final double creditAmount;
  final String? description;
  final int? costCenterId;
  final int? projectId;

  CreateJournalEntryLineRequest({
    required this.accountId,
    required this.debitAmount,
    required this.creditAmount,
    this.description,
    this.costCenterId,
    this.projectId,
  });

  Map<String, dynamic> toJson() {
    return {
      'account_id': accountId,
      'debit_amount': debitAmount,
      'credit_amount': creditAmount,
      'description': description,
      'cost_center_id': costCenterId,
      'project_id': projectId,
    };
  }
}

/// نموذج فلتر القيود اليومية
/// Journal Entry Filter Model
class JournalEntryFilter {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? status;
  final String? referenceType;
  final int? accountId;
  final int pageNumber;
  final int pageSize;

  JournalEntryFilter({
    this.fromDate,
    this.toDate,
    this.status,
    this.referenceType,
    this.accountId,
    this.pageNumber = 1,
    this.pageSize = 50,
  });

  Map<String, String> toQueryParameters() {
    final Map<String, String> params = {};
    
    if (fromDate != null) {
      params['fromDate'] = fromDate!.toIso8601String();
    }
    if (toDate != null) {
      params['toDate'] = toDate!.toIso8601String();
    }
    if (status != null && status!.isNotEmpty) {
      params['status'] = status!;
    }
    if (referenceType != null && referenceType!.isNotEmpty) {
      params['referenceType'] = referenceType!;
    }
    if (accountId != null) {
      params['accountId'] = accountId.toString();
    }
    params['pageNumber'] = pageNumber.toString();
    params['pageSize'] = pageSize.toString();

    return params;
  }
}
