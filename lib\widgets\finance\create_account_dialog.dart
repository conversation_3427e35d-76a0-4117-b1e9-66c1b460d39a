import 'package:flutter/material.dart';
import '../../models/finance/account.dart';
import '../../services/finance/accounting_service.dart';

/// حوار إنشاء حساب جديد
/// Create Account Dialog
class CreateAccountDialog extends StatefulWidget {
  final AccountingService accountingService;
  final List<Account> parentAccounts;

  const CreateAccountDialog({
    Key? key,
    required this.accountingService,
    required this.parentAccounts,
  }) : super(key: key);

  @override
  State<CreateAccountDialog> createState() => _CreateAccountDialogState();
}

class _CreateAccountDialogState extends State<CreateAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _accountCodeController = TextEditingController();
  final _accountNameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedAccountType = 'asset';
  String _selectedBalanceType = 'debit';
  int? _selectedParentAccountId;
  bool _isLoading = false;

  final List<Map<String, String>> _accountTypes = [
    {'value': 'asset', 'label': 'أصول'},
    {'value': 'liability', 'label': 'خصوم'},
    {'value': 'equity', 'label': 'حقوق ملكية'},
    {'value': 'revenue', 'label': 'إيرادات'},
    {'value': 'expense', 'label': 'مصروفات'},
  ];

  final List<Map<String, String>> _balanceTypes = [
    {'value': 'debit', 'label': 'مدين'},
    {'value': 'credit', 'label': 'دائن'},
  ];

  @override
  void dispose() {
    _accountCodeController.dispose();
    _accountNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _createAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = CreateAccountRequest(
        accountCode: _accountCodeController.text.trim(),
        accountName: _accountNameController.text.trim(),
        accountType: _selectedAccountType,
        parentAccountId: _selectedParentAccountId,
        balanceType: _selectedBalanceType,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
      );

      final account = await widget.accountingService.createAccount(request);
      
      if (mounted) {
        Navigator.of(context).pop(account);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الحساب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إنشاء حساب جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // رمز الحساب
                TextFormField(
                  controller: _accountCodeController,
                  decoration: const InputDecoration(
                    labelText: 'رمز الحساب *',
                    hintText: 'مثال: 1001',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'رمز الحساب مطلوب';
                    }
                    if (value.trim().length < 2) {
                      return 'رمز الحساب يجب أن يكون على الأقل حرفين';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // اسم الحساب
                TextFormField(
                  controller: _accountNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الحساب *',
                    hintText: 'مثال: النقدية في الصندوق',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'اسم الحساب مطلوب';
                    }
                    if (value.trim().length < 3) {
                      return 'اسم الحساب يجب أن يكون على الأقل 3 أحرف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // نوع الحساب
                DropdownButtonFormField<String>(
                  value: _selectedAccountType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الحساب *',
                    border: OutlineInputBorder(),
                  ),
                  items: _accountTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type['value'],
                      child: Text(type['label']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedAccountType = value!;
                      // تحديد نوع الرصيد تلقائياً حسب نوع الحساب
                      if (['asset', 'expense'].contains(value)) {
                        _selectedBalanceType = 'debit';
                      } else {
                        _selectedBalanceType = 'credit';
                      }
                    });
                  },
                ),
                const SizedBox(height: 16),

                // نوع الرصيد
                DropdownButtonFormField<String>(
                  value: _selectedBalanceType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الرصيد *',
                    border: OutlineInputBorder(),
                  ),
                  items: _balanceTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type['value'],
                      child: Text(type['label']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedBalanceType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // الحساب الأب (اختياري)
                if (widget.parentAccounts.isNotEmpty)
                  DropdownButtonFormField<int>(
                    value: _selectedParentAccountId,
                    decoration: const InputDecoration(
                      labelText: 'الحساب الأب (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem<int>(
                        value: null,
                        child: Text('لا يوجد (حساب رئيسي)'),
                      ),
                      ...widget.parentAccounts.map((account) {
                        return DropdownMenuItem<int>(
                          value: account.id,
                          child: Text(account.displayName),
                        );
                      }).toList(),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedParentAccountId = value;
                      });
                    },
                  ),
                const SizedBox(height: 16),

                // الوصف (اختياري)
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'وصف مختصر للحساب',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 500,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createAccount,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إنشاء'),
        ),
      ],
    );
  }
}
