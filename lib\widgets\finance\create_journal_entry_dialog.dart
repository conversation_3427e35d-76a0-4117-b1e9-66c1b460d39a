import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/finance/journal_entry.dart';
import '../../models/finance/account.dart';
import '../../services/finance/accounting_service.dart';

/// حوار إنشاء قيد يومي جديد
/// Create Journal Entry Dialog
class CreateJournalEntryDialog extends StatefulWidget {
  final AccountingService accountingService;

  const CreateJournalEntryDialog({
    Key? key,
    required this.accountingService,
  }) : super(key: key);

  @override
  State<CreateJournalEntryDialog> createState() => _CreateJournalEntryDialogState();
}

class _CreateJournalEntryDialogState extends State<CreateJournalEntryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _referenceNumberController = TextEditingController();

  DateTime _entryDate = DateTime.now();
  String? _referenceType;
  List<JournalEntryLineData> _lines = [];
  List<Account> _accounts = [];
  bool _isLoading = false;
  bool _isLoadingAccounts = true;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
    _addNewLine();
    _addNewLine(); // إضافة خطين افتراضيين
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _referenceNumberController.dispose();
    for (var line in _lines) {
      line.dispose();
    }
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    try {
      final accounts = await widget.accountingService.getAllAccounts();
      setState(() {
        _accounts = accounts;
        _isLoadingAccounts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingAccounts = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل الحسابات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _addNewLine() {
    setState(() {
      _lines.add(JournalEntryLineData());
    });
  }

  void _removeLine(int index) {
    if (_lines.length > 2) {
      setState(() {
        _lines[index].dispose();
        _lines.removeAt(index);
      });
    }
  }

  double get _totalDebit {
    return _lines.fold(0.0, (sum, line) => sum + (line.debitAmount ?? 0));
  }

  double get _totalCredit {
    return _lines.fold(0.0, (sum, line) => sum + (line.creditAmount ?? 0));
  }

  bool get _isBalanced {
    return (_totalDebit - _totalCredit).abs() < 0.01;
  }

  Future<void> _createJournalEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_lines.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة خطين على الأقل للقيد'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!_isBalanced) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final lines = _lines.where((line) => 
        line.accountId != null && 
        ((line.debitAmount ?? 0) > 0 || (line.creditAmount ?? 0) > 0)
      ).map((line) => CreateJournalEntryLineRequest(
        accountId: line.accountId!,
        debitAmount: line.debitAmount ?? 0,
        creditAmount: line.creditAmount ?? 0,
        description: line.description,
      )).toList();

      final request = CreateJournalEntryRequest(
        entryDate: _entryDate,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        referenceType: _referenceType,
        referenceNumber: _referenceNumberController.text.trim().isEmpty 
            ? null 
            : _referenceNumberController.text.trim(),
        lines: lines,
      );

      final journalEntry = await widget.accountingService.createJournalEntry(request);
      
      if (mounted) {
        Navigator.of(context).pop(journalEntry);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء القيد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إنشاء قيد يومي جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // معلومات القيد الأساسية
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // تاريخ القيد
                      Row(
                        children: [
                          Expanded(
                            child: ListTile(
                              title: const Text('تاريخ القيد'),
                              subtitle: Text('${_entryDate.day}/${_entryDate.month}/${_entryDate.year}'),
                              leading: const Icon(Icons.calendar_today),
                              onTap: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: _entryDate,
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now().add(const Duration(days: 365)),
                                );
                                if (date != null) {
                                  setState(() {
                                    _entryDate = date;
                                  });
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // الوصف
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف القيد (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // الرقم المرجعي
                      TextFormField(
                        controller: _referenceNumberController,
                        decoration: const InputDecoration(
                          labelText: 'الرقم المرجعي (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // ملخص الأرصدة
              Card(
                color: _isBalanced ? Colors.green[50] : Colors.red[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Text(
                              'إجمالي المدين',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              '${_totalDebit.toStringAsFixed(2)} ر.س',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.red[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          children: [
                            Text(
                              'إجمالي الدائن',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              '${_totalCredit.toStringAsFixed(2)} ر.س',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          children: [
                            Text(
                              'الفرق',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _isBalanced ? Icons.check_circle : Icons.warning,
                                  size: 16,
                                  color: _isBalanced ? Colors.green : Colors.red,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _isBalanced ? 'متوازن' : '${(_totalDebit - _totalCredit).toStringAsFixed(2)}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: _isBalanced ? Colors.green : Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // خطوط القيد
              Expanded(
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Text(
                          'خطوط القيد',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: _addNewLine,
                          icon: const Icon(Icons.add),
                          tooltip: 'إضافة خط جديد',
                        ),
                      ],
                    ),
                    
                    Expanded(
                      child: _isLoadingAccounts
                          ? const Center(child: CircularProgressIndicator())
                          : ListView.builder(
                              itemCount: _lines.length,
                              itemBuilder: (context, index) {
                                return _buildJournalEntryLine(index);
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading || !_isBalanced ? null : _createJournalEntry,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إنشاء القيد'),
        ),
      ],
    );
  }

  Widget _buildJournalEntryLine(int index) {
    final line = _lines[index];
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'الخط ${index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (_lines.length > 2)
                  IconButton(
                    onPressed: () => _removeLine(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    iconSize: 20,
                  ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // اختيار الحساب
            DropdownButtonFormField<int>(
              value: line.accountId,
              decoration: const InputDecoration(
                labelText: 'الحساب *',
                border: OutlineInputBorder(),
                isDense: true,
              ),
              items: _accounts.map((account) {
                return DropdownMenuItem<int>(
                  value: account.id,
                  child: Text(
                    account.displayName,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  line.accountId = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يجب اختيار حساب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 8),
            
            // المبالغ
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: line.debitController,
                    decoration: const InputDecoration(
                      labelText: 'مدين',
                      border: OutlineInputBorder(),
                      isDense: true,
                      suffixText: 'ر.س',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        line.debitAmount = double.tryParse(value);
                        if (line.debitAmount != null && line.debitAmount! > 0) {
                          line.creditController.clear();
                          line.creditAmount = null;
                        }
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: line.creditController,
                    decoration: const InputDecoration(
                      labelText: 'دائن',
                      border: OutlineInputBorder(),
                      isDense: true,
                      suffixText: 'ر.س',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        line.creditAmount = double.tryParse(value);
                        if (line.creditAmount != null && line.creditAmount! > 0) {
                          line.debitController.clear();
                          line.debitAmount = null;
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // وصف الخط
            TextFormField(
              controller: line.descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الخط (اختياري)',
                border: OutlineInputBorder(),
                isDense: true,
              ),
              onChanged: (value) {
                line.description = value.trim().isEmpty ? null : value.trim();
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// فئة بيانات خط القيد
class JournalEntryLineData {
  int? accountId;
  double? debitAmount;
  double? creditAmount;
  String? description;
  
  final TextEditingController debitController = TextEditingController();
  final TextEditingController creditController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  
  void dispose() {
    debitController.dispose();
    creditController.dispose();
    descriptionController.dispose();
  }
}
