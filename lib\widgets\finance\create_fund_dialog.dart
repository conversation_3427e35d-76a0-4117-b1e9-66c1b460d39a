import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/finance/fund.dart';
import '../../services/finance/fund_management_service.dart';

/// حوار إنشاء صندوق جديد
/// Create Fund Dialog
class CreateFundDialog extends StatefulWidget {
  final FundManagementService fundManagementService;

  const CreateFundDialog({
    Key? key,
    required this.fundManagementService,
  }) : super(key: key);

  @override
  State<CreateFundDialog> createState() => _CreateFundDialogState();
}

class _CreateFundDialogState extends State<CreateFundDialog> {
  final _formKey = GlobalKey<FormState>();
  final _fundCodeController = TextEditingController();
  final _fundNameController = TextEditingController();
  final _openingBalanceController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedFundType = 'cash';
  bool _isLoading = false;

  final List<Map<String, String>> _fundTypes = [
    {'value': 'cash', 'label': 'نقدي'},
    {'value': 'bank', 'label': 'بنكي'},
    {'value': 'petty_cash', 'label': 'نثرية'},
  ];

  @override
  void dispose() {
    _fundCodeController.dispose();
    _fundNameController.dispose();
    _openingBalanceController.dispose();
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _createFund() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = CreateFundRequest(
        fundCode: _fundCodeController.text.trim(),
        fundName: _fundNameController.text.trim(),
        accountId: 1, // TODO: ربط بحساب محاسبي حقيقي
        openingBalance: double.tryParse(_openingBalanceController.text.trim()) ?? 0,
        fundType: _selectedFundType,
        bankName: _selectedFundType == 'bank' && _bankNameController.text.trim().isNotEmpty
            ? _bankNameController.text.trim()
            : null,
        accountNumber: _selectedFundType == 'bank' && _accountNumberController.text.trim().isNotEmpty
            ? _accountNumberController.text.trim()
            : null,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
      );

      final fund = await widget.fundManagementService.createFund(request);
      
      if (mounted) {
        Navigator.of(context).pop(fund);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الصندوق: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إنشاء صندوق جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // رمز الصندوق
                TextFormField(
                  controller: _fundCodeController,
                  decoration: const InputDecoration(
                    labelText: 'رمز الصندوق *',
                    hintText: 'مثال: CASH001',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'رمز الصندوق مطلوب';
                    }
                    if (value.trim().length < 3) {
                      return 'رمز الصندوق يجب أن يكون على الأقل 3 أحرف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // اسم الصندوق
                TextFormField(
                  controller: _fundNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الصندوق *',
                    hintText: 'مثال: الصندوق الرئيسي',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'اسم الصندوق مطلوب';
                    }
                    if (value.trim().length < 3) {
                      return 'اسم الصندوق يجب أن يكون على الأقل 3 أحرف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // نوع الصندوق
                DropdownButtonFormField<String>(
                  value: _selectedFundType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الصندوق *',
                    border: OutlineInputBorder(),
                  ),
                  items: _fundTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type['value'],
                      child: Text(type['label']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFundType = value!;
                      // مسح بيانات البنك عند تغيير النوع
                      if (value != 'bank') {
                        _bankNameController.clear();
                        _accountNumberController.clear();
                      }
                    });
                  },
                ),
                const SizedBox(height: 16),

                // الرصيد الافتتاحي
                TextFormField(
                  controller: _openingBalanceController,
                  decoration: const InputDecoration(
                    labelText: 'الرصيد الافتتاحي',
                    hintText: '0.00',
                    border: OutlineInputBorder(),
                    suffixText: 'ر.س',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      final amount = double.tryParse(value.trim());
                      if (amount == null || amount < 0) {
                        return 'يجب إدخال مبلغ صحيح';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // معلومات البنك (للصناديق البنكية فقط)
                if (_selectedFundType == 'bank') ...[
                  TextFormField(
                    controller: _bankNameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم البنك',
                      hintText: 'مثال: البنك الأهلي السعودي',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (_selectedFundType == 'bank' && (value == null || value.trim().isEmpty)) {
                        return 'اسم البنك مطلوب للصناديق البنكية';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    controller: _accountNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الحساب',
                      hintText: 'مثال: *********',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    validator: (value) {
                      if (_selectedFundType == 'bank' && (value == null || value.trim().isEmpty)) {
                        return 'رقم الحساب مطلوب للصناديق البنكية';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // الوصف (اختياري)
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'وصف مختصر للصندوق',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 500,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createFund,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إنشاء'),
        ),
      ],
    );
  }
}
