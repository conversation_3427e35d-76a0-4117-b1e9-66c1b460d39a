using webApi.Models.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// واجهة خدمة إدارة الصناديق
    /// Fund Management Service Interface
    /// </summary>
    public interface IFundManagementService
    {
        // ===================================================================
        // إدارة الصناديق
        // ===================================================================

        /// <summary>
        /// إنشاء صندوق جديد
        /// </summary>
        Task<Fund> CreateFundAsync(CreateFundRequest request);

        /// <summary>
        /// الحصول على جميع الصناديق
        /// </summary>
        Task<List<Fund>> GetAllFundsAsync();

        /// <summary>
        /// الحصول على صندوق بالمعرف
        /// </summary>
        Task<Fund?> GetFundByIdAsync(int fundId);

        /// <summary>
        /// الحصول على رصيد الصندوق
        /// </summary>
        Task<decimal> GetFundBalanceAsync(int fundId);

        /// <summary>
        /// تحديث رصيد الصندوق
        /// </summary>
        Task<bool> UpdateFundBalanceAsync(int fundId, decimal amount, string transactionType);

        // ===================================================================
        // حركات الصناديق
        // ===================================================================

        /// <summary>
        /// إضافة حركة للصندوق
        /// </summary>
        Task<FundTransaction> AddFundTransactionAsync(CreateFundTransactionRequest request);

        /// <summary>
        /// الحصول على حركات الصندوق
        /// </summary>
        Task<List<FundTransaction>> GetFundTransactionsAsync(int fundId, FundTransactionFilter filter);

        /// <summary>
        /// تحويل بين الصناديق
        /// </summary>
        Task<FundTransferResult> TransferBetweenFundsAsync(FundTransferRequest request);

        // ===================================================================
        // التحويلات التمويلية
        // ===================================================================

        /// <summary>
        /// تحويل من صندوق إلى حساب شخصي
        /// </summary>
        Task<FundingTransfer> TransferToPersonalAccountAsync(FundingTransferRequest request);

        /// <summary>
        /// الحصول على التحويلات التمويلية
        /// </summary>
        Task<List<FundingTransfer>> GetFundingTransfersAsync(FundingTransferFilter filter);

        /// <summary>
        /// اعتماد تحويل تمويلي
        /// </summary>
        Task<bool> ApproveFundingTransferAsync(int transferId, int approvedBy);

        /// <summary>
        /// إكمال تحويل تمويلي
        /// </summary>
        Task<bool> CompleteFundingTransferAsync(int transferId);

        // ===================================================================
        // التقارير
        // ===================================================================

        /// <summary>
        /// تقرير حركة الصناديق
        /// </summary>
        Task<FundMovementReport> GenerateFundMovementReportAsync(int fundId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تقرير أرصدة الصناديق
        /// </summary>
        Task<List<FundBalanceReport>> GetFundBalancesReportAsync(DateTime asOfDate);
    }

    // ===================================================================
    // نماذج الطلبات والاستجابات
    // ===================================================================

    public class CreateFundRequest
    {
        public string FundName { get; set; } = string.Empty;
        public string FundCode { get; set; } = string.Empty;
        public int AccountId { get; set; }
        public int? LocationId { get; set; }
        public int? LegalEntityId { get; set; }
        public decimal OpeningBalance { get; set; }
        public string FundType { get; set; } = "cash";
        public string? BankName { get; set; }
        public string? AccountNumber { get; set; }
        public string? Description { get; set; }
        public int CreatedBy { get; set; }
    }

    public class CreateFundTransactionRequest
    {
        public int FundId { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? ReferenceType { get; set; }
        public int? ReferenceId { get; set; }
        public string? Description { get; set; }
        public DateTime TransactionDate { get; set; }
        public int CreatedBy { get; set; }
    }

    public class FundTransactionFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? TransactionType { get; set; }
        public string? ReferenceType { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    public class FundTransferRequest
    {
        public int SourceFundId { get; set; }
        public int TargetFundId { get; set; }
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public string? ReferenceNumber { get; set; }
        public int CreatedBy { get; set; }
    }

    public class FundTransferResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public FundTransaction? SourceTransaction { get; set; }
        public FundTransaction? TargetTransaction { get; set; }
        public JournalEntry? JournalEntry { get; set; }
    }

    public class FundingTransferRequest
    {
        public int SourceFundId { get; set; }
        public int TargetAccountId { get; set; }
        public decimal Amount { get; set; }
        public DateTime TransferDate { get; set; }
        public string TransferType { get; set; } = "funding";
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public int CreatedBy { get; set; }
    }

    public class FundingTransferFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Status { get; set; }
        public string? TransferType { get; set; }
        public int? SourceFundId { get; set; }
        public int? TargetAccountId { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    public class FundMovementReport
    {
        public Fund Fund { get; set; } = null!;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal TotalDeposits { get; set; }
        public decimal TotalWithdrawals { get; set; }
        public List<FundTransaction> Transactions { get; set; } = new();
    }

    public class FundBalanceReport
    {
        public int FundId { get; set; }
        public string FundCode { get; set; } = string.Empty;
        public string FundName { get; set; } = string.Empty;
        public string FundType { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public DateTime AsOfDate { get; set; }
        public bool IsActive { get; set; }
    }
}
