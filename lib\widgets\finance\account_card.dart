import 'package:flutter/material.dart';
import '../../models/finance/account.dart';

/// ويدجت بطاقة الحساب المحاسبي
/// Account Card Widget
class AccountCard extends StatelessWidget {
  final Account account;
  final VoidCallback? onTap;
  final bool showHierarchy;
  final int indentLevel;

  const AccountCard({
    Key? key,
    required this.account,
    this.onTap,
    this.showHierarchy = false,
    this.indentLevel = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(
        left: showHierarchy ? (indentLevel * 20.0) : 0,
        bottom: 8,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // الصف الأول: معلومات الحساب الأساسية
              Row(
                children: [
                  // أيقونة نوع الحساب
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getAccountTypeColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getAccountTypeIcon(),
                      color: _getAccountTypeColor(),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات الحساب
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم الحساب
                        Text(
                          account.accountName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        // رمز الحساب ونوعه
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                account.accountCode,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getAccountTypeColor().withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                account.accountTypeText,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: _getAccountTypeColor(),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // الرصيد
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${account.currentBalance.toStringAsFixed(2)} ر.س',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: account.currentBalance >= 0 
                              ? Colors.green[700] 
                              : Colors.red[700],
                        ),
                      ),
                      Text(
                        account.balanceTypeText,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              // الصف الثاني: معلومات إضافية (إذا وجدت)
              if (account.description != null || 
                  account.isPersonalAccount || 
                  account.hasChildren) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 8),
                Row(
                  children: [
                    // الوصف
                    if (account.description != null)
                      Expanded(
                        child: Text(
                          account.description!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    
                    // العلامات والمؤشرات
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // حساب شخصي
                        if (account.isPersonalAccount)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.person,
                                  size: 12,
                                  color: Colors.blue[700],
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'شخصي',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        
                        // له حسابات فرعية
                        if (account.hasChildren) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.account_tree,
                                  size: 12,
                                  color: Colors.orange[700],
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${account.childAccounts.length}',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.orange[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        
                        // حساب نظام
                        if (account.isSystemAccount) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.purple[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.settings,
                                  size: 12,
                                  color: Colors.purple[700],
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'نظام',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.purple[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ],
              
              // الحسابات الفرعية (في العرض الهرمي)
              if (showHierarchy && account.hasChildren) ...[
                const SizedBox(height: 8),
                ...account.childAccounts.map(
                  (childAccount) => AccountCard(
                    account: childAccount,
                    onTap: onTap,
                    showHierarchy: true,
                    indentLevel: indentLevel + 1,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getAccountTypeColor() {
    switch (account.accountType.toLowerCase()) {
      case 'asset':
        return Colors.green;
      case 'liability':
        return Colors.red;
      case 'equity':
        return Colors.blue;
      case 'revenue':
        return Colors.teal;
      case 'expense':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getAccountTypeIcon() {
    switch (account.accountType.toLowerCase()) {
      case 'asset':
        return Icons.account_balance_wallet;
      case 'liability':
        return Icons.credit_card;
      case 'equity':
        return Icons.account_balance;
      case 'revenue':
        return Icons.trending_up;
      case 'expense':
        return Icons.trending_down;
      default:
        return Icons.account_circle;
    }
  }
}
