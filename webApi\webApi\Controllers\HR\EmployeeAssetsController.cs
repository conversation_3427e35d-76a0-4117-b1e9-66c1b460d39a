using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services;
using webApi.Services.Finance;
using webApi.Models.Finance;
using System.ComponentModel.DataAnnotations;

namespace webApi.Controllers.HR
{
    /// <summary>
    /// تحكم في عهد الموظفين
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmployeeAssetsController : ControllerBase
    {
        private readonly IAssetManagementService _assetManagementService;
        private readonly ILogger<EmployeeAssetsController> _logger;
        private readonly ILoggingService _loggingService;

        public EmployeeAssetsController(
            IAssetManagementService assetManagementService,
            ILogger<EmployeeAssetsController> logger,
            ILoggingService loggingService)
        {
            _assetManagementService = assetManagementService;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// تسليم عهدة لموظف
        /// </summary>
        [HttpPost("{assetId}/assign/{employeeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Asset>> AssignAssetToEmployee(int assetId, int employeeId, [FromBody] AssignAssetRequest? request = null)
        {
            try
            {
                var asset = await _assetManagementService.AssignAssetToEmployeeAsync(assetId, employeeId, request?.Notes);

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "تسليم عهدة",
                    "Asset",
                    assetId,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم تسليم العهدة {asset.AssetName} للموظف {employeeId}"
                );

                return Ok(asset);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسليم العهدة {AssetId} للموظف {EmployeeId}", assetId, employeeId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على عهد موظف
        /// </summary>
        [HttpGet("employee/{employeeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<Asset>>> GetEmployeeAssets(int employeeId, [FromQuery] string? status = null)
        {
            try
            {
                var assets = await _assetManagementService.GetEmployeeAssetsAsync(employeeId, status);
                return Ok(assets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على عهد الموظف {EmployeeId}", employeeId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// استرداد عهدة من موظف
        /// </summary>
        [HttpPost("{assetId}/return")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> ReturnAssetFromEmployee(int assetId, [FromBody] ReturnAssetRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _assetManagementService.ReturnAssetFromEmployeeAsync(
                    assetId, 
                    request.ReturnCondition, 
                    request.Notes);

                if (success)
                {
                    // تسجيل العملية
                    await _loggingService.LogActivityAsync(
                        "استرداد عهدة",
                        "Asset",
                        assetId,
                        1, // TODO: الحصول على معرف المستخدم الحالي
                        $"تم استرداد العهدة {assetId}"
                    );

                    return Ok(new { message = "تم استرداد العهدة بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "العهدة غير موجودة أو غير مسلمة لموظف" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرداد العهدة {AssetId}", assetId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// نقل عهدة بين موظفين
        /// </summary>
        [HttpPost("{assetId}/transfer")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> TransferAssetBetweenEmployees(int assetId, [FromBody] TransferAssetRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _assetManagementService.TransferAssetBetweenEmployeesAsync(
                    assetId, 
                    request.FromEmployeeId, 
                    request.ToEmployeeId, 
                    request.Reason);

                if (success)
                {
                    // تسجيل العملية
                    await _loggingService.LogActivityAsync(
                        "نقل عهدة",
                        "Asset",
                        assetId,
                        1, // TODO: الحصول على معرف المستخدم الحالي
                        $"تم نقل العهدة {assetId} من الموظف {request.FromEmployeeId} إلى الموظف {request.ToEmployeeId}"
                    );

                    return Ok(new { message = "تم نقل العهدة بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "فشل في نقل العهدة" });
                }
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في نقل العهدة {AssetId}", assetId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على تاريخ نقل العهدة
        /// </summary>
        [HttpGet("{assetId}/history")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<AssetTransfer>>> GetAssetTransferHistory(int assetId)
        {
            try
            {
                var history = await _assetManagementService.GetTransferHistoryAsync(assetId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ العهدة {AssetId}", assetId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على ملخص عهد الموظفين
        /// </summary>
        [HttpGet("summary")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<EmployeeAssetSummary>>> GetEmployeeAssetsSummary([FromQuery] int? departmentId = null)
        {
            try
            {
                var summaries = await _assetManagementService.GetEmployeeAssetsSummaryAsync(departmentId);
                return Ok(summaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على ملخص عهد الموظفين");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على جميع الأصول المتاحة للتسليم
        /// </summary>
        [HttpGet("available")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<Asset>>> GetAvailableAssets()
        {
            try
            {
                var filter = new AssetFilter
                {
                    Status = "available" // الأصول المتاحة فقط
                };

                var assets = await _assetManagementService.GetAllAssetsAsync(filter);
                return Ok(assets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصول المتاحة");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }
    }

    // ===== نماذج الطلبات =====

    /// <summary>
    /// طلب تسليم عهدة
    /// </summary>
    public class AssignAssetRequest
    {
        public string? Notes { get; set; }
    }

    /// <summary>
    /// طلب استرداد عهدة
    /// </summary>
    public class ReturnAssetRequest
    {
        public string? ReturnCondition { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// طلب نقل عهدة
    /// </summary>
    public class TransferAssetRequest
    {
        [Required(ErrorMessage = "معرف الموظف المرسل مطلوب")]
        public int FromEmployeeId { get; set; }

        [Required(ErrorMessage = "معرف الموظف المستلم مطلوب")]
        public int ToEmployeeId { get; set; }

        public string? Reason { get; set; }
    }
}
