using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;
using webApi.DTOs.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// خدمة إدارة الحسابات الشخصية للموظفين
    /// </summary>
    public interface IPersonalAccountService
    {
        Task<PersonalAccount> CreatePersonalAccountAsync(CreatePersonalAccountRequest request);
        Task<PersonalAccount> GetPersonalAccountAsync(int accountId);
        Task<PersonalAccount?> GetPersonalAccountByEmployeeAsync(int employeeId);
        Task<List<PersonalAccount>> GetAllPersonalAccountsAsync(int? departmentId = null);
        Task<PersonalAccount> UpdatePersonalAccountAsync(int accountId, UpdatePersonalAccountRequest request);
        Task<bool> DeletePersonalAccountAsync(int accountId);
        Task<PersonalAccountTransaction> AddTransactionAsync(CreatePersonalAccountTransactionRequest request);
        Task<List<PersonalAccountTransaction>> GetAccountTransactionsAsync(int accountId, DateTime? startDate = null, DateTime? endDate = null);
        Task<PersonalAccountStatement> GenerateAccountStatementAsync(int accountId, DateTime startDate, DateTime endDate);
        Task<bool> TransferToPersonalAccountAsync(int fromFundId, int toAccountId, decimal amount, string description);
        Task<bool> TransferFromPersonalAccountAsync(int fromAccountId, int toFundId, decimal amount, string description);
        Task<decimal> GetAccountBalanceAsync(int accountId);
        Task<List<PersonalAccountSummary>> GetAccountsSummaryAsync(int? departmentId = null);
    }

    public class PersonalAccountService : IPersonalAccountService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<PersonalAccountService> _logger;
        private readonly ILoggingService _loggingService;
        private readonly IFundManagementService _fundManagementService;

        public PersonalAccountService(
            TasksDbContext context,
            ILogger<PersonalAccountService> logger,
            ILoggingService loggingService,
            IFundManagementService fundManagementService)
        {
            _context = context;
            _logger = logger;
            _loggingService = loggingService;
            _fundManagementService = fundManagementService;
        }

        /// <summary>
        /// إنشاء حساب شخصي جديد
        /// </summary>
        public async Task<PersonalAccount> CreatePersonalAccountAsync(CreatePersonalAccountRequest request)
        {
            try
            {
                // التحقق من عدم وجود حساب شخصي للموظف
                var existingAccount = await _context.PersonalAccounts
                    .FirstOrDefaultAsync(pa => pa.PersonId == request.EmployeeId);

                if (existingAccount != null)
                    throw new InvalidOperationException($"يوجد حساب شخصي للموظف {request.EmployeeId} مسبقاً");

                // التحقق من وجود الموظف
                var employee = await _context.Users.FindAsync(request.EmployeeId);
                if (employee == null)
                    throw new ArgumentException($"الموظف غير موجود: {request.EmployeeId}");

                var personalAccount = new PersonalAccount
                {
                    PersonId = request.EmployeeId,
                    AccountCode = await GenerateAccountCodeAsync(),
                    AccountName = request.AccountName ?? $"حساب {employee.FullNameArabic ?? employee.Name}",
                    AccountType = request.AccountType ?? "employee",
                    CurrentBalance = request.InitialBalance,
                    IsActive = true,
                    Description = request.Notes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.PersonalAccounts.Add(personalAccount);
                await _context.SaveChangesAsync();

                // إضافة معاملة الرصيد الافتتاحي إذا كان أكبر من صفر
                if (request.InitialBalance > 0)
                {
                    await AddTransactionAsync(new CreatePersonalAccountTransactionRequest
                    {
                        PersonalAccountId = personalAccount.Id,
                        TransactionType = "credit",
                        Amount = request.InitialBalance,
                        Description = "الرصيد الافتتاحي",
                        ReferenceType = "opening_balance",
                        CreatedBy = request.CreatedBy
                    });
                }

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "إنشاء حساب شخصي",
                    "PersonalAccount",
                    personalAccount.Id,
                    request.CreatedBy,
                    $"تم إنشاء حساب شخصي للموظف {employee.FullNameArabic ?? employee.Name} برقم {personalAccount.AccountCode}"
                );

                return personalAccount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء حساب شخصي للموظف {EmployeeId}", request.EmployeeId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على حساب شخصي
        /// </summary>
        public async Task<PersonalAccount> GetPersonalAccountAsync(int accountId)
        {
            try
            {
                var account = await _context.PersonalAccounts
                    .Include(pa => pa.Person)
                    .FirstOrDefaultAsync(pa => pa.Id == accountId);

                if (account == null)
                    throw new ArgumentException($"الحساب الشخصي غير موجود: {accountId}");

                return account;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحساب الشخصي {AccountId}", accountId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على حساب شخصي بواسطة معرف الموظف
        /// </summary>
        public async Task<PersonalAccount?> GetPersonalAccountByEmployeeAsync(int employeeId)
        {
            try
            {
                return await _context.PersonalAccounts
                    .Include(pa => pa.Person)
                    .FirstOrDefaultAsync(pa => pa.PersonId == employeeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحساب الشخصي للموظف {EmployeeId}", employeeId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الحسابات الشخصية
        /// </summary>
        public async Task<List<PersonalAccount>> GetAllPersonalAccountsAsync(int? departmentId = null)
        {
            try
            {
                var query = _context.PersonalAccounts
                    .Include(pa => pa.Person)
                        .ThenInclude(e => e.Department)
                    .Where(pa => pa.IsActive);

                if (departmentId.HasValue)
                {
                    query = query.Where(pa => pa.Person.DepartmentId == departmentId.Value);
                }

                return await query.OrderBy(pa => pa.AccountCode).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحسابات الشخصية");
                throw;
            }
        }

        /// <summary>
        /// تحديث حساب شخصي
        /// </summary>
        public async Task<PersonalAccount> UpdatePersonalAccountAsync(int accountId, UpdatePersonalAccountRequest request)
        {
            try
            {
                var account = await _context.PersonalAccounts.FindAsync(accountId);
                if (account == null)
                    throw new ArgumentException($"الحساب الشخصي غير موجود: {accountId}");

                // تحديث البيانات
                if (!string.IsNullOrEmpty(request.AccountName))
                    account.AccountName = request.AccountName;

                if (!string.IsNullOrEmpty(request.AccountType))
                    account.AccountType = request.AccountType;

                if (request.IsActive.HasValue)
                    account.IsActive = request.IsActive.Value;

                if (!string.IsNullOrEmpty(request.Notes))
                    account.Description = request.Notes;

                account.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "تحديث حساب شخصي",
                    "PersonalAccount",
                    accountId,
                    request.UpdatedBy,
                    $"تم تحديث الحساب الشخصي رقم {account.AccountCode}"
                );

                return account;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الحساب الشخصي {AccountId}", accountId);
                throw;
            }
        }

        /// <summary>
        /// حذف حساب شخصي
        /// </summary>
        public async Task<bool> DeletePersonalAccountAsync(int accountId)
        {
            try
            {
                var account = await _context.PersonalAccounts.FindAsync(accountId);
                if (account == null)
                    return false;

                // التحقق من عدم وجود معاملات
                var hasTransactions = await _context.PersonalAccountTransactions
                    .AnyAsync(pat => pat.AccountId == accountId);

                if (hasTransactions)
                    throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على معاملات");

                // حذف ناعم
                account.IsActive = false;
                account.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "حذف حساب شخصي",
                    "PersonalAccount",
                    accountId,
                    1, // TODO: الحصول على معرف المستخدم الحالي
                    $"تم حذف الحساب الشخصي رقم {account.AccountCode}"
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الحساب الشخصي {AccountId}", accountId);
                throw;
            }
        }

        /// <summary>
        /// إضافة معاملة للحساب الشخصي
        /// </summary>
        public async Task<PersonalAccountTransaction> AddTransactionAsync(CreatePersonalAccountTransactionRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var account = await _context.PersonalAccounts.FindAsync(request.PersonalAccountId);
                if (account == null)
                    throw new ArgumentException($"الحساب الشخصي غير موجود: {request.PersonalAccountId}");

                if (!account.IsActive)
                    throw new InvalidOperationException("الحساب الشخصي غير نشط");

                // التحقق من صحة المعاملة
                if (request.TransactionType == "debit" && account.CurrentBalance < request.Amount)
                    throw new InvalidOperationException($"الرصيد غير كافي. الرصيد الحالي: {account.CurrentBalance:C}");

                var accountTransaction = new PersonalAccountTransaction
                {
                    AccountId = request.PersonalAccountId,
                    TransactionType = request.TransactionType,
                    Amount = request.Amount,
                    Description = request.Description,
                    ReferenceType = request.ReferenceType,
                    ReferenceId = request.ReferenceId,
                    ReferenceNumber = request.ReferenceNumber,
                    TransactionDate = request.TransactionDate ?? DateTime.UtcNow,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                // تحديث رصيد الحساب
                if (request.TransactionType == "credit")
                {
                    account.CurrentBalance += request.Amount;
                }
                else if (request.TransactionType == "debit")
                {
                    account.CurrentBalance -= request.Amount;
                }

                // تحديث الرصيد بعد المعاملة في سجل المعاملة
                accountTransaction.BalanceAfter = account.CurrentBalance;

                account.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                _context.PersonalAccountTransactions.Add(accountTransaction);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("تمت إضافة معاملة للحساب الشخصي {AccountId}: {TransactionType} بمبلغ {Amount:C}",
                    request.PersonalAccountId, request.TransactionType, request.Amount);

                return accountTransaction;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في إضافة معاملة للحساب الشخصي {AccountId}", request.PersonalAccountId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على معاملات الحساب
        /// </summary>
        public async Task<List<PersonalAccountTransaction>> GetAccountTransactionsAsync(int accountId, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = _context.PersonalAccountTransactions
                    .Where(pat => pat.AccountId == accountId);

                if (startDate.HasValue)
                    query = query.Where(pat => pat.TransactionDate.Date >= startDate.Value.Date);

                if (endDate.HasValue)
                    query = query.Where(pat => pat.TransactionDate.Date <= endDate.Value.Date);

                return await query.OrderByDescending(pat => pat.TransactionDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معاملات الحساب {AccountId}", accountId);
                throw;
            }
        }

        /// <summary>
        /// توليد كشف حساب
        /// </summary>
        public async Task<PersonalAccountStatement> GenerateAccountStatementAsync(int accountId, DateTime startDate, DateTime endDate)
        {
            try
            {
                var account = await GetPersonalAccountAsync(accountId);
                var transactions = await GetAccountTransactionsAsync(accountId, startDate, endDate);

                var statement = new PersonalAccountStatement
                {
                    AccountId = accountId,
                    AccountNumber = account.AccountCode,
                    AccountName = account.AccountName,
                    EmployeeName = account.Person?.FullNameArabic ?? account.Person?.Name ?? "غير محدد",
                    StartDate = startDate,
                    EndDate = endDate,
                    GeneratedAt = DateTime.UtcNow,
                    Transactions = transactions.Select(t => new PersonalAccountStatementTransaction
                    {
                        TransactionDate = t.TransactionDate,
                        TransactionType = t.TransactionType,
                        Amount = t.Amount,
                        Description = t.Description,
                        ReferenceNumber = t.ReferenceNumber
                    }).ToList()
                };

                // حساب الأرصدة
                statement.OpeningBalance = await CalculateBalanceAsOfDateAsync(accountId, startDate.AddDays(-1));
                statement.TotalDebits = transactions.Where(t => t.TransactionType == "credit").Sum(t => t.Amount);
                statement.TotalCredits = transactions.Where(t => t.TransactionType == "debit").Sum(t => t.Amount);
                statement.ClosingBalance = statement.OpeningBalance + statement.TotalDebits - statement.TotalCredits;

                return statement;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد كشف حساب للحساب {AccountId}", accountId);
                throw;
            }
        }

        /// <summary>
        /// تحويل من صندوق إلى حساب شخصي
        /// </summary>
        public async Task<bool> TransferToPersonalAccountAsync(int fromFundId, int toAccountId, decimal amount, string description)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // خصم من الصندوق
                await _fundManagementService.AddFundTransactionAsync(new CreateFundTransactionRequest
                {
                    FundId = fromFundId,
                    TransactionType = "transfer_out",
                    Amount = amount,
                    Description = $"تحويل إلى حساب شخصي - {description}",
                    ReferenceType = "personal_account_transfer",
                    ReferenceId = toAccountId,
                    TransactionDate = DateTime.UtcNow,
                    CreatedBy = 1 // TODO: الحصول على معرف المستخدم الحالي
                });

                // إضافة للحساب الشخصي
                await AddTransactionAsync(new CreatePersonalAccountTransactionRequest
                {
                    PersonalAccountId = toAccountId,
                    TransactionType = "credit",
                    Amount = amount,
                    Description = $"تحويل من صندوق - {description}",
                    ReferenceType = "fund_transfer",
                    ReferenceId = fromFundId,
                    CreatedBy = 1 // TODO: الحصول على معرف المستخدم الحالي
                });

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في التحويل من الصندوق {FundId} إلى الحساب الشخصي {AccountId}", fromFundId, toAccountId);
                throw;
            }
        }

        /// <summary>
        /// تحويل من حساب شخصي إلى صندوق
        /// </summary>
        public async Task<bool> TransferFromPersonalAccountAsync(int fromAccountId, int toFundId, decimal amount, string description)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // خصم من الحساب الشخصي
                await AddTransactionAsync(new CreatePersonalAccountTransactionRequest
                {
                    PersonalAccountId = fromAccountId,
                    TransactionType = "debit",
                    Amount = amount,
                    Description = $"تحويل إلى صندوق - {description}",
                    ReferenceType = "fund_transfer",
                    ReferenceId = toFundId,
                    CreatedBy = 1 // TODO: الحصول على معرف المستخدم الحالي
                });

                // إضافة للصندوق
                await _fundManagementService.AddFundTransactionAsync(new CreateFundTransactionRequest
                {
                    FundId = toFundId,
                    TransactionType = "transfer_in",
                    Amount = amount,
                    Description = $"تحويل من حساب شخصي - {description}",
                    ReferenceType = "personal_account_transfer",
                    ReferenceId = fromAccountId,
                    TransactionDate = DateTime.UtcNow,
                    CreatedBy = 1 // TODO: الحصول على معرف المستخدم الحالي
                });

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في التحويل من الحساب الشخصي {AccountId} إلى الصندوق {FundId}", fromAccountId, toFundId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على رصيد الحساب
        /// </summary>
        public async Task<decimal> GetAccountBalanceAsync(int accountId)
        {
            try
            {
                var account = await _context.PersonalAccounts.FindAsync(accountId);
                return account?.CurrentBalance ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رصيد الحساب {AccountId}", accountId);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على ملخص الحسابات
        /// </summary>
        public async Task<List<PersonalAccountSummary>> GetAccountsSummaryAsync(int? departmentId = null)
        {
            try
            {
                var query = _context.PersonalAccounts
                    .Include(pa => pa.Person)
                        .ThenInclude(e => e.Department)
                    .Where(pa => pa.IsActive);

                if (departmentId.HasValue)
                {
                    query = query.Where(pa => pa.Person.DepartmentId == departmentId.Value);
                }

                var accounts = await query.ToListAsync();

                var summaries = new List<PersonalAccountSummary>();

                foreach (var account in accounts)
                {
                    var transactionCount = await _context.PersonalAccountTransactions
                        .CountAsync(pat => pat.AccountId == account.Id);

                    var lastTransaction = await _context.PersonalAccountTransactions
                        .Where(pat => pat.AccountId == account.Id)
                        .OrderByDescending(pat => pat.TransactionDate)
                        .FirstOrDefaultAsync();

                    summaries.Add(new PersonalAccountSummary
                    {
                        AccountId = account.Id,
                        AccountNumber = account.AccountCode,
                        AccountName = account.AccountName,
                        EmployeeName = account.Person?.FullNameArabic ?? account.Person?.Name ?? "غير محدد",
                        DepartmentName = account.Person?.Department?.Name ?? "غير محدد",
                        CurrentBalance = account.CurrentBalance,
                        Currency = "SAR", // إزالة account.Currency لأنه غير موجود
                        TransactionCount = transactionCount,
                        LastTransactionDate = lastTransaction?.TransactionDate,
                        IsActive = account.IsActive
                    });
                }

                return summaries.OrderBy(s => s.AccountNumber).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على ملخص الحسابات");
                throw;
            }
        }

        // ===== الدوال المساعدة =====

        private async Task<string> GenerateAccountCodeAsync()
        {
            var lastAccount = await _context.PersonalAccounts
                .OrderByDescending(pa => pa.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastAccount?.Id ?? 0) + 1;
            return $"PA{nextNumber:D6}"; // PA000001, PA000002, etc.
        }

        private async Task<decimal> CalculateBalanceAsOfDateAsync(int accountId, DateTime asOfDate)
        {
            var account = await _context.PersonalAccounts.FindAsync(accountId);
            if (account == null) return 0;

            var transactions = await _context.PersonalAccountTransactions
                .Where(pat => pat.AccountId == accountId && pat.TransactionDate.Date <= asOfDate.Date)
                .ToListAsync();

            var totalCredits = transactions
                .Where(t => t.TransactionType == "credit")
                .Sum(t => t.Amount);

            var totalDebits = transactions
                .Where(t => t.TransactionType == "debit")
                .Sum(t => t.Amount);

            return totalCredits - totalDebits; // الرصيد = الدائن - المدين
        }
    }
}
