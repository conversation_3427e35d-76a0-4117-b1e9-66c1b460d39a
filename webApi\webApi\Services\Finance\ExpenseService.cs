using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;
using webApi.DTOs.Finance;

namespace webApi.Services
{
    /// <summary>
    /// خدمة إدارة بنود الصرف
    /// Expense Management Service
    /// </summary>
    public interface IExpenseService
    {
        // Expense Item Types
        Task<List<ExpenseItemType>> GetExpenseItemTypesAsync();
        Task<ExpenseItemType?> GetExpenseItemTypeByIdAsync(int id);
        Task<ExpenseItemType> CreateExpenseItemTypeAsync(CreateExpenseItemTypeDto dto, int userId);
        Task<ExpenseItemType> UpdateExpenseItemTypeAsync(int id, UpdateExpenseItemTypeDto dto, int userId);
        Task<bool> DeleteExpenseItemTypeAsync(int id);
        Task<List<ExpenseItemType>> GetExpenseItemTypeHierarchyAsync();

        // Expense Templates
        Task<List<ExpenseTemplate>> GetExpenseTemplatesAsync(int? expenseItemTypeId = null);
        Task<ExpenseTemplate?> GetExpenseTemplateByIdAsync(int id);
        Task<ExpenseTemplate> CreateExpenseTemplateAsync(CreateExpenseTemplateDto dto, int userId);
        Task<ExpenseTemplate> UpdateExpenseTemplateAsync(int id, UpdateExpenseTemplateDto dto, int userId);
        Task<bool> DeleteExpenseTemplateAsync(int id);

        // Expenses
        Task<List<Expense>> GetExpensesAsync(ExpenseFilterDto? filter = null);
        Task<Expense?> GetExpenseByIdAsync(int id);
        Task<Expense> CreateExpenseAsync(CreateExpenseDto dto, int userId);
        Task<Expense> UpdateExpenseAsync(int id, UpdateExpenseDto dto, int userId);
        Task<bool> DeleteExpenseAsync(int id);
        Task<Expense> SubmitExpenseAsync(int id, int userId);
        Task<Expense> ApproveExpenseAsync(int id, int approverId, string? comments = null);
        Task<Expense> RejectExpenseAsync(int id, int rejectorId, string reason);
        Task<JournalEntry> GenerateJournalEntryAsync(int expenseId, int userId);
    }

    public class ExpenseService : IExpenseService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<ExpenseService> _logger;

        public ExpenseService(TasksDbContext context, ILogger<ExpenseService> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Expense Item Types

        public async Task<List<ExpenseItemType>> GetExpenseItemTypesAsync()
        {
            return await _context.ExpenseItemTypes
                .Include(x => x.Parent)
                .Include(x => x.Children)
                .Include(x => x.DefaultAccount)
                .Where(x => x.IsActive)
                .OrderBy(x => x.Level)
                .ThenBy(x => x.SortOrder)
                .ToListAsync();
        }

        public async Task<ExpenseItemType?> GetExpenseItemTypeByIdAsync(int id)
        {
            return await _context.ExpenseItemTypes
                .Include(x => x.Parent)
                .Include(x => x.Children)
                .Include(x => x.DefaultAccount)
                .Include(x => x.Creator)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<ExpenseItemType> CreateExpenseItemTypeAsync(CreateExpenseItemTypeDto dto, int userId)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // تحديد المستوى
            int level = 0;
            if (dto.ParentId.HasValue)
            {
                var parent = await _context.ExpenseItemTypes.FindAsync(dto.ParentId.Value);
                if (parent == null)
                    throw new ArgumentException("Parent expense item type not found");
                
                level = parent.Level + 1;
                
                // تحديث الوالد ليصبح غير ورقة
                parent.IsLeaf = false;
                _context.ExpenseItemTypes.Update(parent);
            }

            var expenseItemType = new ExpenseItemType
            {
                Name = dto.Name,
                NameArabic = dto.NameArabic,
                Description = dto.Description,
                Code = dto.Code,
                ParentId = dto.ParentId,
                Level = level,
                SortOrder = dto.SortOrder,
                IsActive = dto.IsActive,
                IsLeaf = dto.IsLeaf,
                ApprovalWorkflow = dto.ApprovalWorkflow,
                RequiresBudgetCheck = dto.RequiresBudgetCheck,
                DefaultAccountId = dto.DefaultAccountId,
                MaxAmount = dto.MaxAmount,
                MinAmount = dto.MinAmount,
                Icon = dto.Icon,
                Color = dto.Color,
                CreatedBy = userId,
                CreatedAt = timestamp
            };

            if (dto.DataSchema != null && dto.DataSchema.Any())
            {
                expenseItemType.SetDataSchema(dto.DataSchema);
            }

            _context.ExpenseItemTypes.Add(expenseItemType);
            await _context.SaveChangesAsync();

            return await GetExpenseItemTypeByIdAsync(expenseItemType.Id) ?? expenseItemType;
        }

        public async Task<ExpenseItemType> UpdateExpenseItemTypeAsync(int id, UpdateExpenseItemTypeDto dto, int userId)
        {
            var expenseItemType = await _context.ExpenseItemTypes.FindAsync(id);
            if (expenseItemType == null)
                throw new ArgumentException("Expense item type not found");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            expenseItemType.Name = dto.Name;
            expenseItemType.NameArabic = dto.NameArabic;
            expenseItemType.Description = dto.Description;
            expenseItemType.IsActive = dto.IsActive;
            expenseItemType.ApprovalWorkflow = dto.ApprovalWorkflow;
            expenseItemType.RequiresBudgetCheck = dto.RequiresBudgetCheck;
            expenseItemType.DefaultAccountId = dto.DefaultAccountId;
            expenseItemType.MaxAmount = dto.MaxAmount;
            expenseItemType.MinAmount = dto.MinAmount;
            expenseItemType.Icon = dto.Icon;
            expenseItemType.Color = dto.Color;
            expenseItemType.UpdatedBy = userId;
            expenseItemType.UpdatedAt = timestamp;

            if (dto.DataSchema != null)
            {
                expenseItemType.SetDataSchema(dto.DataSchema);
            }

            _context.ExpenseItemTypes.Update(expenseItemType);
            await _context.SaveChangesAsync();

            return await GetExpenseItemTypeByIdAsync(id) ?? expenseItemType;
        }

        public async Task<bool> DeleteExpenseItemTypeAsync(int id)
        {
            var expenseItemType = await _context.ExpenseItemTypes
                .Include(x => x.Children)
                .Include(x => x.Expenses)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (expenseItemType == null)
                return false;

            // التحقق من وجود أطفال أو مصروفات
            if (expenseItemType.Children.Any() || expenseItemType.Expenses.Any())
            {
                throw new InvalidOperationException("Cannot delete expense item type with children or associated expenses");
            }

            _context.ExpenseItemTypes.Remove(expenseItemType);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<List<ExpenseItemType>> GetExpenseItemTypeHierarchyAsync()
        {
            var allTypes = await _context.ExpenseItemTypes
                .Include(x => x.Children)
                .Include(x => x.DefaultAccount)
                .Where(x => x.IsActive)
                .OrderBy(x => x.Level)
                .ThenBy(x => x.SortOrder)
                .ToListAsync();

            return allTypes.Where(x => x.ParentId == null).ToList();
        }

        #endregion

        #region Expense Templates

        public async Task<List<ExpenseTemplate>> GetExpenseTemplatesAsync(int? expenseItemTypeId = null)
        {
            var query = _context.ExpenseTemplates
                .Include(x => x.ExpenseItemType)
                .Include(x => x.Creator)
                .Where(x => x.IsActive);

            if (expenseItemTypeId.HasValue)
            {
                query = query.Where(x => x.ExpenseItemTypeId == expenseItemTypeId.Value);
            }

            return await query.OrderBy(x => x.NameArabic).ToListAsync();
        }

        public async Task<ExpenseTemplate?> GetExpenseTemplateByIdAsync(int id)
        {
            return await _context.ExpenseTemplates
                .Include(x => x.ExpenseItemType)
                .Include(x => x.Creator)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<ExpenseTemplate> CreateExpenseTemplateAsync(CreateExpenseTemplateDto dto, int userId)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            var template = new ExpenseTemplate
            {
                Name = dto.Name,
                NameArabic = dto.NameArabic,
                Description = dto.Description,
                ExpenseItemTypeId = dto.ExpenseItemTypeId,
                IsActive = dto.IsActive,
                IsDefault = dto.IsDefault,
                CreatedBy = userId,
                CreatedAt = timestamp
            };

            if (dto.TemplateConfig != null)
            {
                template.SetTemplateConfig(dto.TemplateConfig);
            }

            if (dto.ApprovalLevels != null && dto.ApprovalLevels.Any())
            {
                template.SetApprovalLevels(dto.ApprovalLevels);
            }

            if (dto.CalculationRules != null && dto.CalculationRules.Any())
            {
                template.SetCalculationRules(dto.CalculationRules);
            }

            if (dto.DefaultLines != null && dto.DefaultLines.Any())
            {
                template.SetDefaultLines(dto.DefaultLines);
            }

            _context.ExpenseTemplates.Add(template);
            await _context.SaveChangesAsync();

            return await GetExpenseTemplateByIdAsync(template.Id) ?? template;
        }

        public async Task<ExpenseTemplate> UpdateExpenseTemplateAsync(int id, UpdateExpenseTemplateDto dto, int userId)
        {
            var template = await _context.ExpenseTemplates.FindAsync(id);
            if (template == null)
                throw new ArgumentException("Expense template not found");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            template.Name = dto.Name;
            template.NameArabic = dto.NameArabic;
            template.Description = dto.Description;
            template.IsActive = dto.IsActive;
            template.UpdatedBy = userId;
            template.UpdatedAt = timestamp;

            if (dto.TemplateConfig != null)
            {
                template.SetTemplateConfig(dto.TemplateConfig);
            }

            if (dto.ApprovalLevels != null)
            {
                template.SetApprovalLevels(dto.ApprovalLevels);
            }

            if (dto.CalculationRules != null)
            {
                template.SetCalculationRules(dto.CalculationRules);
            }

            if (dto.DefaultLines != null)
            {
                template.SetDefaultLines(dto.DefaultLines);
            }

            _context.ExpenseTemplates.Update(template);
            await _context.SaveChangesAsync();

            return await GetExpenseTemplateByIdAsync(id) ?? template;
        }

        public async Task<bool> DeleteExpenseTemplateAsync(int id)
        {
            var template = await _context.ExpenseTemplates
                .Include(x => x.Expenses)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (template == null)
                return false;

            // التحقق من وجود مصروفات مرتبطة
            if (template.Expenses.Any())
            {
                throw new InvalidOperationException("Cannot delete template with associated expenses");
            }

            _context.ExpenseTemplates.Remove(template);
            await _context.SaveChangesAsync();

            return true;
        }

        #endregion

        #region Expenses

        public async Task<List<Expense>> GetExpensesAsync(ExpenseFilterDto? filter = null)
        {
            var query = _context.Expenses
                .Include(x => x.ExpenseItemType)
                .Include(x => x.ExpenseTemplate)
                .Include(x => x.Requester)
                .Include(x => x.Department)
                .Include(x => x.ExpenseLines)
                .AsQueryable();

            if (filter != null)
            {
                if (filter.Status != null)
                    query = query.Where(x => x.Status == filter.Status);

                if (filter.RequestedBy.HasValue)
                    query = query.Where(x => x.RequestedBy == filter.RequestedBy.Value);

                if (filter.DepartmentId.HasValue)
                    query = query.Where(x => x.DepartmentId == filter.DepartmentId.Value);

                if (filter.ExpenseItemTypeId.HasValue)
                    query = query.Where(x => x.ExpenseItemTypeId == filter.ExpenseItemTypeId.Value);

                if (filter.FromDate.HasValue)
                    query = query.Where(x => x.ExpenseDate >= filter.FromDate.Value);

                if (filter.ToDate.HasValue)
                    query = query.Where(x => x.ExpenseDate <= filter.ToDate.Value);

                if (filter.MinAmount.HasValue)
                    query = query.Where(x => x.TotalAmount >= filter.MinAmount.Value);

                if (filter.MaxAmount.HasValue)
                    query = query.Where(x => x.TotalAmount <= filter.MaxAmount.Value);
            }

            return await query.OrderByDescending(x => x.CreatedAt).ToListAsync();
        }

        public async Task<Expense?> GetExpenseByIdAsync(int id)
        {
            return await _context.Expenses
                .Include(x => x.ExpenseItemType)
                .Include(x => x.ExpenseTemplate)
                .Include(x => x.Requester)
                .Include(x => x.Department)
                .Include(x => x.ExpenseLines)
                    .ThenInclude(l => l.Account)
                .Include(x => x.ExpenseApprovals)
                    .ThenInclude(a => a.Approver)
                .Include(x => x.ExpenseAttachments)
                .Include(x => x.JournalEntry)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<Expense> CreateExpenseAsync(CreateExpenseDto dto, int userId)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            var expense = new Expense
            {
                ExpenseNumber = Expense.GenerateExpenseNumber(),
                Title = dto.Title,
                Description = dto.Description,
                ExpenseItemTypeId = dto.ExpenseItemTypeId,
                ExpenseTemplateId = dto.ExpenseTemplateId,
                RequestedBy = userId,
                DepartmentId = dto.DepartmentId,
                CostCenterId = dto.CostCenterId,
                TaskId = dto.TaskId,
                TotalAmount = 0, // سيتم حسابه من الخطوط
                Currency = dto.Currency ?? "SAR",
                ExchangeRate = dto.ExchangeRate ?? 1.0m,
                ExpenseDate = dto.ExpenseDate,
                Status = "draft",
                Priority = dto.Priority ?? "normal",
                DueDate = dto.DueDate,
                BudgetId = dto.BudgetId,
                BudgetLineId = dto.BudgetLineId,
                PaymentMethod = dto.PaymentMethod,
                CreatedBy = userId,
                CreatedAt = timestamp
            };

            if (dto.CustomFields != null && dto.CustomFields.Any())
            {
                expense.SetCustomFields(dto.CustomFields);
            }

            _context.Expenses.Add(expense);
            await _context.SaveChangesAsync();

            // إضافة الخطوط إذا كانت موجودة
            if (dto.ExpenseLines != null && dto.ExpenseLines.Any())
            {
                foreach (var lineDto in dto.ExpenseLines)
                {
                    var line = new ExpenseLine
                    {
                        ExpenseId = expense.Id,
                        Description = lineDto.Description,
                        AccountId = lineDto.AccountId,
                        Amount = lineDto.Amount,
                        Quantity = lineDto.Quantity,
                        UnitPrice = lineDto.UnitPrice,
                        Unit = lineDto.Unit,
                        TaxRate = lineDto.TaxRate,
                        DiscountRate = lineDto.DiscountRate,
                        CostCenterId = lineDto.CostCenterId,
                        TaskId = lineDto.TaskId,
                        DepartmentId = lineDto.DepartmentId,
                        Reference = lineDto.Reference,
                        LineDate = lineDto.LineDate,
                        SortOrder = lineDto.SortOrder,
                        CreatedAt = timestamp
                    };

                    line.RecalculateAmounts();
                    _context.ExpenseLines.Add(line);
                }

                await _context.SaveChangesAsync();

                // إعادة حساب المجموع
                expense.CalculateTotal();
                _context.Expenses.Update(expense);
                await _context.SaveChangesAsync();
            }

            return await GetExpenseByIdAsync(expense.Id) ?? expense;
        }

        public async Task<Expense> UpdateExpenseAsync(int id, UpdateExpenseDto dto, int userId)
        {
            var expense = await _context.Expenses
                .Include(x => x.ExpenseLines)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (expense == null)
                throw new ArgumentException("Expense not found");

            if (!expense.IsEditable)
                throw new InvalidOperationException("Expense cannot be edited in current status");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            expense.Title = dto.Title;
            expense.Description = dto.Description;
            expense.DepartmentId = dto.DepartmentId;
            expense.CostCenterId = dto.CostCenterId;
            expense.TaskId = dto.TaskId;
            expense.ExpenseDate = dto.ExpenseDate;
            expense.Priority = dto.Priority ?? expense.Priority;
            expense.DueDate = dto.DueDate;
            expense.PaymentMethod = dto.PaymentMethod;
            expense.UpdatedBy = userId;
            expense.UpdatedAt = timestamp;

            if (dto.CustomFields != null)
            {
                expense.SetCustomFields(dto.CustomFields);
            }

            _context.Expenses.Update(expense);
            await _context.SaveChangesAsync();

            return await GetExpenseByIdAsync(id) ?? expense;
        }

        public async Task<bool> DeleteExpenseAsync(int id)
        {
            var expense = await _context.Expenses
                .Include(x => x.ExpenseLines)
                .Include(x => x.ExpenseApprovals)
                .Include(x => x.ExpenseAttachments)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (expense == null)
                return false;

            if (!expense.IsEditable)
                throw new InvalidOperationException("Expense cannot be deleted in current status");

            // حذف الخطوط والاعتمادات والمرفقات
            _context.ExpenseLines.RemoveRange(expense.ExpenseLines);
            _context.ExpenseApprovals.RemoveRange(expense.ExpenseApprovals);
            _context.ExpenseAttachments.RemoveRange(expense.ExpenseAttachments);
            _context.Expenses.Remove(expense);

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<Expense> SubmitExpenseAsync(int id, int userId)
        {
            var expense = await _context.Expenses
                .Include(x => x.ExpenseLines)
                .Include(x => x.ExpenseTemplate)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (expense == null)
                throw new ArgumentException("Expense not found");

            if (!expense.CanBeSubmitted)
                throw new InvalidOperationException("Expense cannot be submitted");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            expense.Status = "submitted";
            expense.SubmittedAt = timestamp;
            expense.UpdatedBy = userId;
            expense.UpdatedAt = timestamp;

            // إنشاء سير عمل الاعتماد
            if (expense.ExpenseTemplate?.ParsedApprovalLevels != null)
            {
                foreach (var level in expense.ExpenseTemplate.ParsedApprovalLevels)
                {
                    foreach (var approverId in level.ApproverIds)
                    {
                        var approval = new ExpenseApproval
                        {
                            ExpenseId = expense.Id,
                            Level = level.Level,
                            LevelName = level.LevelName,
                            ApproverId = approverId,
                            Status = "pending",
                            CreatedAt = timestamp
                        };

                        _context.ExpenseApprovals.Add(approval);
                    }
                }
            }

            _context.Expenses.Update(expense);
            await _context.SaveChangesAsync();

            return await GetExpenseByIdAsync(id) ?? expense;
        }

        public async Task<Expense> ApproveExpenseAsync(int id, int approverId, string? comments = null)
        {
            var expense = await _context.Expenses
                .Include(x => x.ExpenseApprovals)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (expense == null)
                throw new ArgumentException("Expense not found");

            if (!expense.CanBeApproved)
                throw new InvalidOperationException("Expense cannot be approved");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // العثور على الاعتماد المطلوب
            var approval = expense.ExpenseApprovals
                .FirstOrDefault(a => a.ApproverId == approverId && a.Status == "pending");

            if (approval == null)
                throw new ArgumentException("Approval not found or already processed");

            approval.Status = "approved";
            approval.Comments = comments;
            approval.ApprovedAt = timestamp;
            approval.UpdatedAt = timestamp;

            // التحقق من اكتمال جميع الاعتمادات
            var allApproved = expense.ExpenseApprovals.All(a => a.Status == "approved");
            if (allApproved)
            {
                expense.Status = "approved";
                expense.ApprovedBy = approverId;
                expense.ApprovedAt = timestamp;
            }

            expense.UpdatedBy = approverId;
            expense.UpdatedAt = timestamp;

            _context.ExpenseApprovals.Update(approval);
            _context.Expenses.Update(expense);
            await _context.SaveChangesAsync();

            return await GetExpenseByIdAsync(id) ?? expense;
        }

        public async Task<Expense> RejectExpenseAsync(int id, int rejectorId, string reason)
        {
            var expense = await _context.Expenses.FindAsync(id);
            if (expense == null)
                throw new ArgumentException("Expense not found");

            if (!expense.CanBeRejected)
                throw new InvalidOperationException("Expense cannot be rejected");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            expense.Status = "rejected";
            expense.RejectionReason = reason;
            expense.RejectedBy = rejectorId;
            expense.RejectedAt = timestamp;
            expense.UpdatedBy = rejectorId;
            expense.UpdatedAt = timestamp;

            _context.Expenses.Update(expense);
            await _context.SaveChangesAsync();

            return await GetExpenseByIdAsync(id) ?? expense;
        }

        public async Task<JournalEntry> GenerateJournalEntryAsync(int expenseId, int userId)
        {
            var expense = await _context.Expenses
                .Include(x => x.ExpenseLines)
                    .ThenInclude(l => l.Account)
                .FirstOrDefaultAsync(x => x.Id == expenseId);

            if (expense == null)
                throw new ArgumentException("Expense not found");

            if (expense.Status != "approved")
                throw new InvalidOperationException("Only approved expenses can generate journal entries");

            if (expense.JournalEntryId.HasValue)
                throw new InvalidOperationException("Journal entry already exists for this expense");

            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            var journalEntry = new JournalEntry
            {
                EntryNumber = $"JE-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks % 10000:D4}",
                Description = $"قيد صرف - {expense.Title}",
                EntryDate = expense.ExpenseDate,
                ReferenceNumber = expense.ExpenseNumber,
                TotalDebit = expense.TotalAmount,
                TotalCredit = expense.TotalAmount,
                Status = "posted",
                CreatedBy = userId,
                CreatedAt = timestamp,
                PostedBy = userId,
                PostedAt = timestamp
            };

            _context.JournalEntries.Add(journalEntry);
            await _context.SaveChangesAsync();

            // إنشاء خطوط القيد
            int lineNumber = 1;

            // خطوط المدين (المصروفات)
            foreach (var expenseLine in expense.ExpenseLines)
            {
                var debitLine = new JournalEntryLine
                {
                    JournalEntryId = journalEntry.Id,
                    LineOrder = lineNumber++,
                    AccountId = expenseLine.AccountId,
                    Description = expenseLine.Description,
                    DebitAmount = expenseLine.NetAmount,
                    CreditAmount = 0,
                    CostCenterId = expenseLine.CostCenterId,
                    TaskId = expenseLine.TaskId,
                    // DepartmentId not available in JournalEntryLine
                    // Reference not available in JournalEntryLine
                    CreatedAt = timestamp
                };

                _context.JournalEntryLines.Add(debitLine);
            }

            // خط الدائن (الصندوق أو الحساب الدائن)
            // يجب تحديد الحساب الدائن بناءً على طريقة الدفع
            var creditAccountId = await GetCreditAccountForPaymentMethod(expense.PaymentMethod);
            
            var creditLine = new JournalEntryLine
            {
                JournalEntryId = journalEntry.Id,
                LineOrder = lineNumber,
                AccountId = creditAccountId,
                Description = $"دفع صرف - {expense.Title}",
                DebitAmount = 0,
                CreditAmount = expense.TotalAmount,
                // Reference not available in JournalEntryLine
                CreatedAt = timestamp
            };

            _context.JournalEntryLines.Add(creditLine);

            // ربط القيد بالصرف
            expense.JournalEntryId = journalEntry.Id;
            expense.Status = "paid";
            expense.PaymentDate = DateTime.UtcNow;
            expense.PaidAmount = expense.TotalAmount;
            expense.UpdatedBy = userId;
            expense.UpdatedAt = timestamp;

            _context.Expenses.Update(expense);
            await _context.SaveChangesAsync();

            return journalEntry;
        }

        private async Task<int> GetCreditAccountForPaymentMethod(string? paymentMethod)
        {
            // منطق تحديد الحساب الدائن بناءً على طريقة الدفع
            // يمكن تخصيص هذا حسب احتياجات النظام
            
            var account = paymentMethod?.ToLower() switch
            {
                "cash" => await _context.Accounts.FirstOrDefaultAsync(a => a.AccountCode == "1010"), // النقدية
                "bank" => await _context.Accounts.FirstOrDefaultAsync(a => a.AccountCode == "1020"), // البنك
                "check" => await _context.Accounts.FirstOrDefaultAsync(a => a.AccountCode == "1020"), // البنك
                _ => await _context.Accounts.FirstOrDefaultAsync(a => a.AccountCode == "1010") // افتراضي: النقدية
            };

            return account?.Id ?? throw new InvalidOperationException("Credit account not found for payment method");
        }

        #endregion
    }
}
