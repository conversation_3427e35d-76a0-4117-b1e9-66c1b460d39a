import 'package:flutter/material.dart';

import '../../models/finance/fund.dart';
import '../../services/finance/fund_management_service.dart';
import 'package:flutter_application_2/services/api/api_service.dart';
import '../../widgets/finance/fund_card.dart';
import '../../widgets/finance/create_fund_dialog.dart';

/// شاشة إدارة الصناديق
/// Funds Management Screen
class FundsScreen extends StatefulWidget {
  const FundsScreen({Key? key}) : super(key: key);

  @override
  State<FundsScreen> createState() => _FundsScreenState();
}

class _FundsScreenState extends State<FundsScreen> {
  late FundManagementService _fundManagementService;
  List<Fund> _funds = [];
  List<Fund> _filteredFunds = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  String _selectedFundType = 'all';

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fundManagementService = FundManagementService(context.read<ApiService>());
    _loadFunds();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadFunds() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final funds = await _fundManagementService.getAllFunds();
      
      setState(() {
        _funds = funds;
        _filteredFunds = funds;
        _isLoading = false;
      });
      
      _applyFilters();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredFunds = _funds.where((fund) {
        // فلتر البحث
        bool matchesSearch = _searchQuery.isEmpty ||
            fund.fundName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            fund.fundCode.toLowerCase().contains(_searchQuery.toLowerCase());

        // فلتر نوع الصندوق
        bool matchesType = _selectedFundType == 'all' ||
            fund.fundType == _selectedFundType;

        return matchesSearch && matchesType;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  void _onFundTypeChanged(String? type) {
    setState(() {
      _selectedFundType = type ?? 'all';
    });
    _applyFilters();
  }

  Future<void> _showCreateFundDialog() async {
    final result = await showDialog<Fund>(
      context: context,
      builder: (context) => CreateFundDialog(
        fundManagementService: _fundManagementService,
      ),
    );

    if (result != null) {
      _loadFunds();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء الصندوق "${result.fundName}" بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصناديق'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFunds,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الصناديق...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: _onSearchChanged,
                ),
                const SizedBox(height: 12),
                // فلتر نوع الصندوق
                Row(
                  children: [
                    const Text('نوع الصندوق: ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(
                      child: DropdownButton<String>(
                        value: _selectedFundType,
                        isExpanded: true,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                          DropdownMenuItem(value: 'bank', child: Text('بنكي')),
                          DropdownMenuItem(value: 'petty_cash', child: Text('نثرية')),
                        ],
                        onChanged: _onFundTypeChanged,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // المحتوى الرئيسي
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateFundDialog,
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
        tooltip: 'إضافة صندوق جديد',
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل الصناديق...');
    }

    if (_error != null) {
      return ErrorDisplayWidget(
        error: _error!,
        onRetry: _loadFunds,
      );
    }

    if (_filteredFunds.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _selectedFundType != 'all'
                  ? 'لا توجد صناديق تطابق معايير البحث'
                  : 'لا توجد صناديق مسجلة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showCreateFundDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة صندوق جديد'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFunds,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredFunds.length,
        itemBuilder: (context, index) {
          final fund = _filteredFunds[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: FundCard(
              fund: fund,
              onTap: () => _showFundDetails(fund),
              onTransactionTap: () => _showFundTransactions(fund),
            ),
          );
        },
      ),
    );
  }

  void _showFundDetails(Fund fund) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(fund.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('رمز الصندوق', fund.fundCode),
            _buildDetailRow('اسم الصندوق', fund.fundName),
            _buildDetailRow('نوع الصندوق', fund.fundTypeText),
            _buildDetailRow('الرصيد الحالي', '${fund.currentBalance.toStringAsFixed(2)} ر.س'),
            _buildDetailRow('الرصيد الافتتاحي', '${fund.openingBalance.toStringAsFixed(2)} ر.س'),
            if (fund.bankName != null)
              _buildDetailRow('اسم البنك', fund.bankName!),
            if (fund.accountNumber != null)
              _buildDetailRow('رقم الحساب', fund.accountNumber!),
            if (fund.description != null)
              _buildDetailRow('الوصف', fund.description!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showFundTransactions(fund);
            },
            child: const Text('عرض الحركات'),
          ),
        ],
      ),
    );
  }

  void _showFundTransactions(Fund fund) {
    // TODO: فتح شاشة حركات الصندوق
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم فتح شاشة حركات الصندوق: ${fund.fundName}'),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
