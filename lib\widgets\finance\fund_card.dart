import 'package:flutter/material.dart';
import '../../models/finance/fund.dart';

/// ويدجت بطاقة الصندوق
/// Fund Card Widget
class FundCard extends StatelessWidget {
  final Fund fund;
  final VoidCallback? onTap;
  final VoidCallback? onTransactionTap;

  const FundCard({
    Key? key,
    required this.fund,
    this.onTap,
    this.onTransactionTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // الصف الأول: معلومات الصندوق الأساسية
              Row(
                children: [
                  // أيقونة نوع الصندوق
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getFundTypeColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getFundTypeIcon(),
                      color: _getFundTypeColor(),
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  // معلومات الصندوق
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم الصندوق
                        Text(
                          fund.fundName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        // رمز الصندوق ونوعه
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                fund.fundCode,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getFundTypeColor().withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                fund.fundTypeText,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: _getFundTypeColor(),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // الرصيد
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${fund.currentBalance.toStringAsFixed(2)} ر.س',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: fund.currentBalance >= 0 
                              ? Colors.green[700] 
                              : Colors.red[700],
                        ),
                      ),
                      Text(
                        'الرصيد الحالي',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              // الصف الثاني: معلومات إضافية
              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 12),
              
              Row(
                children: [
                  // معلومات البنك (للصناديق البنكية)
                  if (fund.isBankFund && fund.bankName != null)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'البنك: ${fund.bankName}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (fund.accountNumber != null)
                            Text(
                              'رقم الحساب: ${fund.accountNumber}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                    )
                  else if (fund.description != null)
                    Expanded(
                      child: Text(
                        fund.description!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  else
                    const Expanded(child: SizedBox()),
                  
                  // العلامات والمؤشرات
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // صندوق افتراضي
                      if (fund.isDefault)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.amber[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star,
                                size: 12,
                                color: Colors.amber[700],
                              ),
                              const SizedBox(width: 2),
                              Text(
                                'افتراضي',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.amber[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      
                      // حالة النشاط
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: fund.isActive 
                              ? Colors.green[100] 
                              : Colors.red[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          fund.isActive ? 'نشط' : 'غير نشط',
                          style: TextStyle(
                            fontSize: 10,
                            color: fund.isActive 
                                ? Colors.green[700] 
                                : Colors.red[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              // الصف الثالث: أزرار الإجراءات
              const SizedBox(height: 12),
              Row(
                children: [
                  // زر عرض الحركات
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: onTransactionTap,
                      icon: const Icon(Icons.list_alt, size: 16),
                      label: const Text('الحركات'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // زر إضافة حركة
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _showAddTransactionDialog(context),
                      icon: const Icon(Icons.add, size: 16),
                      label: const Text('إضافة حركة'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        backgroundColor: _getFundTypeColor(),
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getFundTypeColor() {
    switch (fund.fundType) {
      case 'cash':
        return Colors.green;
      case 'bank':
        return Colors.blue;
      case 'petty_cash':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getFundTypeIcon() {
    switch (fund.fundType) {
      case 'cash':
        return Icons.money;
      case 'bank':
        return Icons.account_balance;
      case 'petty_cash':
        return Icons.wallet;
      default:
        return Icons.account_balance_wallet;
    }
  }

  void _showAddTransactionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة حركة للصندوق: ${fund.fundName}'),
        content: const Text('سيتم تطوير هذه الوظيفة لاحقاً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
