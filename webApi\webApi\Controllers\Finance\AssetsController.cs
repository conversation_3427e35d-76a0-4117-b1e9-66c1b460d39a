using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services.Finance;
using webApi.Models.Finance;
using webApi.Attributes;

namespace webApi.Controllers.Finance
{
    /// <summary>
    /// متحكم إدارة الأصول
    /// Assets Controller
    /// </summary>
    [Route("api/finance/[controller]")]
    [ApiController]
    [Authorize]
    [Produces("application/json")]
    public class AssetsController : ControllerBase
    {
        private readonly IAssetManagementService _assetManagementService;
        private readonly ILogger<AssetsController> _logger;

        public AssetsController(IAssetManagementService assetManagementService, ILogger<AssetsController> logger)
        {
            _assetManagementService = assetManagementService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الأصول
        /// </summary>
        [HttpGet]
        //[HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<Asset>>> GetAllAssets([FromQuery] AssetFilter filter)
        {
            try
            {
                var assets = await _assetManagementService.GetAllAssetsAsync(filter);
                return Ok(assets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصول");
                return StatusCode(500, new { message = "خطأ في الحصول على الأصول" });
            }
        }

        /// <summary>
        /// الحصول على أصل بالمعرف
        /// </summary>
        [HttpGet("{id}")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<Asset>> GetAssetById(int id)
        {
            try
            {
                var asset = await _assetManagementService.GetAssetByIdAsync(id);
                if (asset == null)
                {
                    return NotFound(new { message = "الأصل غير موجود" });
                }

                return Ok(asset);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على الأصل" });
            }
        }

        /// <summary>
        /// إنشاء أصل جديد
        /// </summary>
        [HttpPost]
        [HasPermission("finance.assets.create")]
        public async Task<ActionResult<Asset>> CreateAsset([FromBody] CreateAssetRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.CreatedBy = GetCurrentUserId();
                var asset = await _assetManagementService.CreateAssetAsync(request);
                return CreatedAtAction(nameof(GetAssetById), new { id = asset.Id }, asset);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الأصل: {AssetCode}", request.AssetCode);
                return StatusCode(500, new { message = "خطأ في إنشاء الأصل" });
            }
        }

        /// <summary>
        /// تحديث أصل
        /// </summary>
        [HttpPut("{id}")]
        [HasPermission("finance.assets.edit")]
        public async Task<ActionResult<Asset>> UpdateAsset(int id, [FromBody] UpdateAssetRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.UpdatedBy = GetCurrentUserId();
                var asset = await _assetManagementService.UpdateAssetAsync(id, request);
                return Ok(asset);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في تحديث الأصل" });
            }
        }

        /// <summary>
        /// حذف أصل
        /// </summary>
        [HttpDelete("{id}")]
        [HasPermission("finance.assets.delete")]
        public async Task<ActionResult> DeleteAsset(int id)
        {
            try
            {
                var success = await _assetManagementService.DeleteAssetAsync(id);
                if (success)
                {
                    return Ok(new { message = "تم حذف الأصل بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "الأصل غير موجود" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في حذف الأصل" });
            }
        }

        /// <summary>
        /// تغيير حالة الأصل
        /// </summary>
        [HttpPost("{id}/change-status")]
        [HasPermission("finance.assets.manage")]
        public async Task<ActionResult> ChangeAssetStatus(int id, [FromBody] ChangeAssetStatusRequest request)
        {
            try
            {
                var success = await _assetManagementService.ChangeAssetStatusAsync(id, request.NewStatus, request.Reason);
                if (success)
                {
                    return Ok(new { message = "تم تغيير حالة الأصل بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "الأصل غير موجود" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير حالة الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في تغيير حالة الأصل" });
            }
        }

        /// <summary>
        /// حساب استهلاك الأصل
        /// </summary>
        [HttpGet("{id}/depreciation/calculate")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<decimal>> CalculateDepreciation(int id, [FromQuery] DateTime asOfDate)
        {
            try
            {
                var depreciation = await _assetManagementService.CalculateDepreciationAsync(id, asOfDate);
                return Ok(new { assetId = id, asOfDate, depreciationAmount = depreciation });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب استهلاك الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في حساب استهلاك الأصل" });
            }
        }

        /// <summary>
        /// تسجيل استهلاك الأصل
        /// </summary>
        [HttpPost("{id}/depreciation")]
        [HasPermission("finance.assets.depreciation")]
        public async Task<ActionResult<AssetDepreciation>> RecordDepreciation(int id, [FromBody] CreateDepreciationRequest request)
        {
            try
            {
                request.AssetId = id;
                request.CreatedBy = GetCurrentUserId();
                
                var depreciation = await _assetManagementService.RecordDepreciationAsync(request);
                return Ok(depreciation);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل استهلاك الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في تسجيل استهلاك الأصل" });
            }
        }

        /// <summary>
        /// الحصول على تاريخ الاستهلاك
        /// </summary>
        [HttpGet("{id}/depreciation")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<AssetDepreciation>>> GetDepreciationHistory(int id)
        {
            try
            {
                var history = await _assetManagementService.GetDepreciationHistoryAsync(id);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ الاستهلاك: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على تاريخ الاستهلاك" });
            }
        }

        /// <summary>
        /// تشغيل الاستهلاك الدوري
        /// </summary>
        [HttpPost("depreciation/run-periodic")]
        [HasPermission("finance.assets.depreciation")]
        public async Task<ActionResult<List<AssetDepreciation>>> RunPeriodicDepreciation([FromQuery] DateTime periodDate)
        {
            try
            {
                var depreciations = await _assetManagementService.RunPeriodicDepreciationAsync(periodDate);
                return Ok(depreciations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشغيل الاستهلاك الدوري");
                return StatusCode(500, new { message = "خطأ في تشغيل الاستهلاك الدوري" });
            }
        }

        /// <summary>
        /// إنشاء سجل صيانة
        /// </summary>
        [HttpPost("{id}/maintenance")]
        [HasPermission("finance.assets.maintenance")]
        public async Task<ActionResult<AssetMaintenance>> CreateMaintenance(int id, [FromBody] CreateMaintenanceRequest request)
        {
            try
            {
                request.AssetId = id;
                request.CreatedBy = GetCurrentUserId();
                
                var maintenance = await _assetManagementService.CreateMaintenanceAsync(request);
                return Ok(maintenance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء سجل الصيانة: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في إنشاء سجل الصيانة" });
            }
        }

        /// <summary>
        /// الحصول على تاريخ الصيانة
        /// </summary>
        [HttpGet("{id}/maintenance")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<AssetMaintenance>>> GetMaintenanceHistory(int id)
        {
            try
            {
                var history = await _assetManagementService.GetMaintenanceHistoryAsync(id);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ الصيانة: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على تاريخ الصيانة" });
            }
        }

        /// <summary>
        /// تحديث حالة الصيانة
        /// </summary>
        [HttpPut("maintenance/{maintenanceId}/status")]
        [HasPermission("finance.assets.maintenance")]
        public async Task<ActionResult> UpdateMaintenanceStatus(int maintenanceId, [FromBody] UpdateMaintenanceStatusRequest request)
        {
            try
            {
                var success = await _assetManagementService.UpdateMaintenanceStatusAsync(maintenanceId, request.NewStatus);
                if (success)
                {
                    return Ok(new { message = "تم تحديث حالة الصيانة بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "سجل الصيانة غير موجود" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث حالة الصيانة: {MaintenanceId}", maintenanceId);
                return StatusCode(500, new { message = "خطأ في تحديث حالة الصيانة" });
            }
        }

        /// <summary>
        /// الحصول على الصيانة المجدولة
        /// </summary>
        [HttpGet("maintenance/scheduled")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<AssetMaintenance>>> GetScheduledMaintenance(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var maintenance = await _assetManagementService.GetScheduledMaintenanceAsync(fromDate, toDate);
                return Ok(maintenance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الصيانة المجدولة");
                return StatusCode(500, new { message = "خطأ في الحصول على الصيانة المجدولة" });
            }
        }

        /// <summary>
        /// إنشاء تقييم للأصل
        /// </summary>
        [HttpPost("{id}/valuation")]
        [HasPermission("finance.assets.valuation")]
        public async Task<ActionResult<AssetValuation>> CreateValuation(int id, [FromBody] CreateValuationRequest request)
        {
            try
            {
                request.AssetId = id;
                request.CreatedBy = GetCurrentUserId();
                
                var valuation = await _assetManagementService.CreateValuationAsync(request);
                return Ok(valuation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقييم الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في إنشاء تقييم الأصل" });
            }
        }

        /// <summary>
        /// الحصول على تاريخ التقييمات
        /// </summary>
        [HttpGet("{id}/valuation")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<AssetValuation>>> GetValuationHistory(int id)
        {
            try
            {
                var history = await _assetManagementService.GetValuationHistoryAsync(id);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ التقييمات: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على تاريخ التقييمات" });
            }
        }

        /// <summary>
        /// نقل أصل
        /// </summary>
        [HttpPost("{id}/transfer")]
        [HasPermission("finance.assets.transfer")]
        public async Task<ActionResult<AssetTransfer>> TransferAsset(int id, [FromBody] CreateTransferRequest request)
        {
            try
            {
                request.AssetId = id;
                request.CreatedBy = GetCurrentUserId();
                
                var transfer = await _assetManagementService.TransferAssetAsync(request);
                return Ok(transfer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في نقل الأصل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في نقل الأصل" });
            }
        }

        /// <summary>
        /// الحصول على تاريخ النقل
        /// </summary>
        [HttpGet("{id}/transfer")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<AssetTransfer>>> GetTransferHistory(int id)
        {
            try
            {
                var history = await _assetManagementService.GetTransferHistoryAsync(id);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ النقل: {AssetId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على تاريخ النقل" });
            }
        }

        /// <summary>
        /// اعتماد نقل الأصل
        /// </summary>
        [HttpPost("transfer/{transferId}/approve")]
        [HasPermission("finance.assets.approve_transfer")]
        public async Task<ActionResult> ApproveTransfer(int transferId)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _assetManagementService.ApproveTransferAsync(transferId, userId);

                if (success)
                {
                    return Ok(new { message = "تم اعتماد النقل بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "سجل النقل غير موجود" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد نقل الأصل: {TransferId}", transferId);
                return StatusCode(500, new { message = "خطأ في اعتماد نقل الأصل" });
            }
        }

        /// <summary>
        /// تقرير الأصول حسب الفئة
        /// </summary>
        [HttpGet("reports/by-category")]
        [HasPermission("finance.reports.view")]
        public async Task<ActionResult<List<AssetCategoryReport>>> GetAssetsByCategoryReport()
        {
            try
            {
                var report = await _assetManagementService.GetAssetsByCategoryReportAsync();
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير الأصول حسب الفئة");
                return StatusCode(500, new { message = "خطأ في إنتاج تقرير الأصول حسب الفئة" });
            }
        }

        /// <summary>
        /// تقرير الاستهلاك
        /// </summary>
        [HttpGet("reports/depreciation")]
        [HasPermission("finance.reports.view")]
        public async Task<ActionResult<List<DepreciationReport>>> GetDepreciationReport(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            try
            {
                var report = await _assetManagementService.GetDepreciationReportAsync(fromDate, toDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير الاستهلاك");
                return StatusCode(500, new { message = "خطأ في إنتاج تقرير الاستهلاك" });
            }
        }

        /// <summary>
        /// تقرير الصيانة
        /// </summary>
        [HttpGet("reports/maintenance")]
        [HasPermission("finance.reports.view")]
        public async Task<ActionResult<List<MaintenanceReport>>> GetMaintenanceReport(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            try
            {
                var report = await _assetManagementService.GetMaintenanceReportAsync(fromDate, toDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير الصيانة");
                return StatusCode(500, new { message = "خطأ في إنتاج تقرير الصيانة" });
            }
        }

        /// <summary>
        /// الأصول منتهية الصلاحية
        /// </summary>
        [HttpGet("expiring")]
        [HasPermission("finance.assets.view")]
        public async Task<ActionResult<List<Asset>>> GetExpiringAssets([FromQuery] int daysAhead = 30)
        {
            try
            {
                var assets = await _assetManagementService.GetExpiringAssetsAsync(daysAhead);
                return Ok(assets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصول منتهية الصلاحية");
                return StatusCode(500, new { message = "خطأ في الحصول على الأصول منتهية الصلاحية" });
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("user_id")?.Value;
            return int.TryParse(userIdClaim, out int userId) ? userId : 0;
        }
    }

    // ===================================================================
    // نماذج الطلبات الإضافية
    // ===================================================================

    public class ChangeAssetStatusRequest
    {
        public string NewStatus { get; set; } = string.Empty;
        public string? Reason { get; set; }
    }

    public class UpdateMaintenanceStatusRequest
    {
        public string NewStatus { get; set; } = string.Empty;
    }
}
