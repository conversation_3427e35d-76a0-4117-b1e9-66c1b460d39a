using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services;
using webApi.Services.Finance;
using webApi.DTOs.Finance;
using webApi.Models.Finance;
using System.ComponentModel.DataAnnotations;

namespace webApi.Controllers.Finance
{
    /// <summary>
    /// تحكم في الحسابات الشخصية للموظفين
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PersonalAccountsController : ControllerBase
    {
        private readonly IPersonalAccountService _personalAccountService;
        private readonly ILogger<PersonalAccountsController> _logger;
        private readonly ILoggingService _loggingService;

        public PersonalAccountsController(
            IPersonalAccountService personalAccountService,
            ILogger<PersonalAccountsController> logger,
            ILoggingService loggingService)
        {
            _personalAccountService = personalAccountService;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// إنشاء حساب شخصي جديد
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<PersonalAccount>> CreatePersonalAccount([FromBody] CreatePersonalAccountRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // TODO: الحصول على معرف المستخدم الحالي من JWT
                request.CreatedBy = 1; // مؤقت

                var account = await _personalAccountService.CreatePersonalAccountAsync(request);
                return CreatedAtAction(nameof(GetPersonalAccount), new { id = account.Id }, account);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء حساب شخصي للموظف {EmployeeId}", request.EmployeeId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على حساب شخصي
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PersonalAccount>> GetPersonalAccount(int id)
        {
            try
            {
                var account = await _personalAccountService.GetPersonalAccountAsync(id);
                return Ok(account);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحساب الشخصي {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على حساب شخصي بواسطة معرف الموظف
        /// </summary>
        [HttpGet("by-employee/{employeeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PersonalAccount>> GetPersonalAccountByEmployee(int employeeId)
        {
            try
            {
                var account = await _personalAccountService.GetPersonalAccountByEmployeeAsync(employeeId);
                if (account == null)
                {
                    return NotFound(new { message = $"لا يوجد حساب شخصي للموظف {employeeId}" });
                }
                return Ok(account);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحساب الشخصي للموظف {EmployeeId}", employeeId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على جميع الحسابات الشخصية
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<PersonalAccount>>> GetAllPersonalAccounts([FromQuery] int? departmentId = null)
        {
            try
            {
                var accounts = await _personalAccountService.GetAllPersonalAccountsAsync(departmentId);
                return Ok(accounts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الحسابات الشخصية");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تحديث حساب شخصي
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PersonalAccount>> UpdatePersonalAccount(int id, [FromBody] UpdatePersonalAccountRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // TODO: الحصول على معرف المستخدم الحالي من JWT
                request.UpdatedBy = 1; // مؤقت

                var account = await _personalAccountService.UpdatePersonalAccountAsync(id, request);
                return Ok(account);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الحساب الشخصي {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// حذف حساب شخصي
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> DeletePersonalAccount(int id)
        {
            try
            {
                var success = await _personalAccountService.DeletePersonalAccountAsync(id);
                if (success)
                {
                    return Ok(new { message = "تم حذف الحساب الشخصي بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "الحساب الشخصي غير موجود" });
                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الحساب الشخصي {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// إضافة معاملة للحساب الشخصي
        /// </summary>
        [HttpPost("{id}/transactions")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PersonalAccountTransaction>> AddTransaction(int id, [FromBody] CreatePersonalAccountTransactionRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                request.PersonalAccountId = id;
                // TODO: الحصول على معرف المستخدم الحالي من JWT
                request.CreatedBy = 1; // مؤقت

                var transaction = await _personalAccountService.AddTransactionAsync(request);
                return CreatedAtAction(nameof(GetAccountTransactions), new { id = id }, transaction);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة معاملة للحساب الشخصي {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على معاملات الحساب
        /// </summary>
        [HttpGet("{id}/transactions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<PersonalAccountTransaction>>> GetAccountTransactions(
            int id, 
            [FromQuery] DateTime? startDate = null, 
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var transactions = await _personalAccountService.GetAccountTransactionsAsync(id, startDate, endDate);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معاملات الحساب {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// توليد كشف حساب
        /// </summary>
        [HttpPost("{id}/statement")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<PersonalAccountStatement>> GenerateAccountStatement(
            int id, 
            [FromBody] AccountStatementRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var statement = await _personalAccountService.GenerateAccountStatementAsync(id, request.StartDate, request.EndDate);
                return Ok(statement);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد كشف حساب للحساب {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تحويل من صندوق إلى حساب شخصي
        /// </summary>
        [HttpPost("transfer-from-fund")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> TransferFromFund([FromBody] TransferToPersonalAccountRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _personalAccountService.TransferToPersonalAccountAsync(
                    request.FromFundId, 
                    request.ToAccountId, 
                    request.Amount, 
                    request.Description);

                if (success)
                {
                    return Ok(new { message = "تم التحويل بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "فشل في التحويل" });
                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحويل من الصندوق {FundId} إلى الحساب {AccountId}", 
                    request.FromFundId, request.ToAccountId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تحويل من حساب شخصي إلى صندوق
        /// </summary>
        [HttpPost("transfer-to-fund")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> TransferToFund([FromBody] TransferFromPersonalAccountRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _personalAccountService.TransferFromPersonalAccountAsync(
                    request.FromAccountId, 
                    request.ToFundId, 
                    request.Amount, 
                    request.Description);

                if (success)
                {
                    return Ok(new { message = "تم التحويل بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "فشل في التحويل" });
                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحويل من الحساب {AccountId} إلى الصندوق {FundId}", 
                    request.FromAccountId, request.ToFundId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على رصيد الحساب
        /// </summary>
        [HttpGet("{id}/balance")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<decimal>> GetAccountBalance(int id)
        {
            try
            {
                var balance = await _personalAccountService.GetAccountBalanceAsync(id);
                return Ok(new { accountId = id, balance = balance });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رصيد الحساب {AccountId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على ملخص الحسابات
        /// </summary>
        [HttpGet("summary")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<PersonalAccountSummary>>> GetAccountsSummary([FromQuery] int? departmentId = null)
        {
            try
            {
                var summaries = await _personalAccountService.GetAccountsSummaryAsync(departmentId);
                return Ok(summaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على ملخص الحسابات");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }
    }

    /// <summary>
    /// طلب كشف حساب
    /// </summary>
    public class AccountStatementRequest
    {
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "تاريخ النهاية مطلوب")]
        public DateTime EndDate { get; set; }
    }
}
