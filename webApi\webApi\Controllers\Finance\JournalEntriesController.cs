using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services.Finance;
using webApi.Models.Finance;
using webApi.Attributes;

namespace webApi.Controllers.Finance
{
    /// <summary>
    /// متحكم إدارة القيود اليومية
    /// Journal Entries Controller
    /// </summary>
    [Route("api/finance/[controller]")]
    [ApiController]
    [Authorize]
    [Produces("application/json")]
    public class JournalEntriesController : ControllerBase
    {
        private readonly IAccountingService _accountingService;
        private readonly ILogger<JournalEntriesController> _logger;

        public JournalEntriesController(IAccountingService accountingService, ILogger<JournalEntriesController> logger)
        {
            _accountingService = accountingService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على القيود اليومية
        /// </summary>
        [HttpGet]
        [HasPermission("finance.journal_entries.view")]
        public async Task<ActionResult<List<JournalEntry>>> GetJournalEntries([FromQuery] JournalEntryFilter filter)
        {
            try
            {
                var entries = await _accountingService.GetJournalEntriesAsync(filter);
                return Ok(entries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على القيود اليومية");
                return StatusCode(500, new { message = "خطأ في الحصول على القيود اليومية" });
            }
        }

        /// <summary>
        /// الحصول على قيد يومي بالمعرف
        /// </summary>
        [HttpGet("{id}")]
        [HasPermission("finance.journal_entries.view")]
        public async Task<ActionResult<JournalEntry>> GetJournalEntryById(int id)
        {
            try
            {
                var filter = new JournalEntryFilter { PageSize = 1 };
                var entries = await _accountingService.GetJournalEntriesAsync(filter);
                var entry = entries.FirstOrDefault(e => e.Id == id);

                if (entry == null)
                {
                    return NotFound(new { message = "القيد غير موجود" });
                }

                return Ok(entry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على القيد: {EntryId}", id);
                return StatusCode(500, new { message = "خطأ في الحصول على القيد" });
            }
        }

        /// <summary>
        /// إنشاء قيد يومي جديد
        /// </summary>
        [HttpPost]
        [HasPermission("finance.journal_entries.create")]
        public async Task<ActionResult<JournalEntry>> CreateJournalEntry([FromBody] CreateJournalEntryRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // التحقق من وجود خطوط القيد
                if (request.Lines == null || !request.Lines.Any())
                {
                    return BadRequest(new { message = "يجب إضافة خطوط للقيد" });
                }

                // التحقق من التوازن المبدئي
                var totalDebit = request.Lines.Sum(l => l.DebitAmount);
                var totalCredit = request.Lines.Sum(l => l.CreditAmount);

                if (Math.Abs(totalDebit - totalCredit) > 0.01m)
                {
                    return BadRequest(new { message = "القيد غير متوازن", totalDebit, totalCredit });
                }

                var entry = await _accountingService.CreateJournalEntryAsync(request);
                return CreatedAtAction(nameof(GetJournalEntryById), new { id = entry.Id }, entry);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء القيد اليومي");
                return StatusCode(500, new { message = "خطأ في إنشاء القيد اليومي" });
            }
        }

        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        [HttpPost("{id}/post")]
        [HasPermission("finance.journal_entries.post")]
        public async Task<ActionResult> PostJournalEntry(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _accountingService.PostJournalEntryAsync(id, userId);

                if (!success)
                {
                    return BadRequest(new { message = "لا يمكن ترحيل القيد. تأكد من أن القيد في حالة مسودة ومتوازن" });
                }

                return Ok(new { message = "تم ترحيل القيد بنجاح" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في ترحيل القيد: {EntryId}", id);
                return StatusCode(500, new { message = "خطأ في ترحيل القيد" });
            }
        }

        /// <summary>
        /// إلغاء قيد يومي
        /// </summary>
        [HttpPost("{id}/cancel")]
        [HasPermission("finance.journal_entries.cancel")]
        public async Task<ActionResult> CancelJournalEntry(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _accountingService.CancelJournalEntryAsync(id, userId);

                if (!success)
                {
                    return BadRequest(new { message = "لا يمكن إلغاء القيد" });
                }

                return Ok(new { message = "تم إلغاء القيد بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إلغاء القيد: {EntryId}", id);
                return StatusCode(500, new { message = "خطأ في إلغاء القيد" });
            }
        }

        /// <summary>
        /// التحقق من توازن القيد
        /// </summary>
        [HttpPost("validate")]
        [HasPermission("finance.journal_entries.view")]
        public async Task<ActionResult> ValidateJournalEntry([FromBody] CreateJournalEntryRequest request)
        {
            try
            {
                if (request.Lines == null || !request.Lines.Any())
                {
                    return BadRequest(new { message = "يجب إضافة خطوط للقيد", isBalanced = false });
                }

                var totalDebit = request.Lines.Sum(l => l.DebitAmount);
                var totalCredit = request.Lines.Sum(l => l.CreditAmount);
                var isBalanced = Math.Abs(totalDebit - totalCredit) < 0.01m;

                return Ok(new 
                { 
                    isBalanced, 
                    totalDebit, 
                    totalCredit, 
                    difference = totalDebit - totalCredit,
                    message = isBalanced ? "القيد متوازن" : "القيد غير متوازن"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من توازن القيد");
                return StatusCode(500, new { message = "خطأ في التحقق من توازن القيد" });
            }
        }

        /// <summary>
        /// تقرير ميزان المراجعة
        /// </summary>
        [HttpGet("trial-balance")]
        [HasPermission("finance.reports.view")]
        public async Task<ActionResult<TrialBalanceReport>> GetTrialBalance([FromQuery] DateTime? asOfDate = null)
        {
            try
            {
                var report = await _accountingService.GenerateTrialBalanceAsync(asOfDate ?? DateTime.Now);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير ميزان المراجعة");
                return StatusCode(500, new { message = "خطأ في إنتاج تقرير ميزان المراجعة" });
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("user_id")?.Value;
            return int.TryParse(userIdClaim, out int userId) ? userId : 0;
        }
    }
}
