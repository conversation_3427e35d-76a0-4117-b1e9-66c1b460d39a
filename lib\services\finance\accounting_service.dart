import 'dart:convert';
import 'package:flutter_application_2/services/api/api_service.dart';
import 'package:http/http.dart' as http;
import '../../models/finance/account.dart';
import '../../models/finance/journal_entry.dart';


/// خدمة المحاسبة في Frontend
/// Accounting Service for Frontend
class AccountingService {
  final ApiService _apiService;
  static const String _baseUrl = '/api/finance';

  AccountingService(this._apiService);

  // ===================================================================
  // إدارة الحسابات (Chart of Accounts)
  // ===================================================================

  /// الحصول على جميع الحسابات
  Future<List<Account>> getAllAccounts() async {
    try {
      final response = await _apiService.get('$_baseUrl/accounts');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => Account.fromJson(item)).toList();
      } else {
        throw Exception('فشل في الحصول على الحسابات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على الحسابات: $e');
    }
  }

  /// الحصول على الهيكل الهرمي للحسابات
  Future<List<Account>> getAccountsHierarchy() async {
    try {
      final response = await _apiService.get('$_baseUrl/accounts/hierarchy');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => Account.fromJson(item)).toList();
      } else {
        throw Exception('فشل في الحصول على الهيكل الهرمي: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على الهيكل الهرمي: $e');
    }
  }

  /// الحصول على حساب بالمعرف
  Future<Account?> getAccountById(int accountId) async {
    try {
      final response = await _apiService.get('$_baseUrl/accounts/$accountId');
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return Account.fromJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('فشل في الحصول على الحساب: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على الحساب: $e');
    }
  }

  /// الحصول على رصيد الحساب
  Future<double> getAccountBalance(int accountId, {DateTime? asOfDate}) async {
    try {
      String url = '$_baseUrl/accounts/$accountId/balance';
      if (asOfDate != null) {
        url += '?asOfDate=${asOfDate.toIso8601String()}';
      }

      final response = await _apiService.get(url);
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return (data['balance'] ?? 0).toDouble();
      } else {
        throw Exception('فشل في الحصول على رصيد الحساب: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على رصيد الحساب: $e');
    }
  }

  /// إنشاء حساب جديد
  Future<Account> createAccount(CreateAccountRequest request) async {
    try {
      final response = await _apiService.post(
        '$_baseUrl/accounts',
        body: json.encode(request.toJson()),
      );
      
      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return Account.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      throw Exception('خطأ في إنشاء الحساب: $e');
    }
  }

  /// الحصول على أرصدة جميع الحسابات
  Future<List<AccountBalance>> getAccountBalances({DateTime? asOfDate}) async {
    try {
      String url = '$_baseUrl/accounts/balances';
      if (asOfDate != null) {
        url += '?asOfDate=${asOfDate.toIso8601String()}';
      }

      final response = await _apiService.get(url);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => AccountBalance.fromJson(item)).toList();
      } else {
        throw Exception('فشل في الحصول على أرصدة الحسابات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على أرصدة الحسابات: $e');
    }
  }

  // ===================================================================
  // إدارة القيود اليومية (Journal Entries)
  // ===================================================================

  /// الحصول على القيود اليومية
  Future<List<JournalEntry>> getJournalEntries(JournalEntryFilter filter) async {
    try {
      final queryParams = filter.toQueryParameters();
      final uri = Uri.parse('$_baseUrl/journalentries').replace(queryParameters: queryParams);
      
      final response = await _apiService.get(uri.toString());
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => JournalEntry.fromJson(item)).toList();
      } else {
        throw Exception('فشل في الحصول على القيود اليومية: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على القيود اليومية: $e');
    }
  }

  /// الحصول على قيد يومي بالمعرف
  Future<JournalEntry?> getJournalEntryById(int entryId) async {
    try {
      final response = await _apiService.get('$_baseUrl/journalentries/$entryId');
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return JournalEntry.fromJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('فشل في الحصول على القيد: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على القيد: $e');
    }
  }

  /// إنشاء قيد يومي جديد
  Future<JournalEntry> createJournalEntry(CreateJournalEntryRequest request) async {
    try {
      final response = await _apiService.post(
        '$_baseUrl/journalentries',
        body: json.encode(request.toJson()),
      );
      
      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return JournalEntry.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في إنشاء القيد');
      }
    } catch (e) {
      throw Exception('خطأ في إنشاء القيد: $e');
    }
  }

  /// ترحيل قيد يومي
  Future<bool> postJournalEntry(int entryId) async {
    try {
      final response = await _apiService.post('$_baseUrl/journalentries/$entryId/post');
      
      if (response.statusCode == 200) {
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في ترحيل القيد');
      }
    } catch (e) {
      throw Exception('خطأ في ترحيل القيد: $e');
    }
  }

  /// إلغاء قيد يومي
  Future<bool> cancelJournalEntry(int entryId) async {
    try {
      final response = await _apiService.post('$_baseUrl/journalentries/$entryId/cancel');
      
      if (response.statusCode == 200) {
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في إلغاء القيد');
      }
    } catch (e) {
      throw Exception('خطأ في إلغاء القيد: $e');
    }
  }

  /// التحقق من توازن القيد
  Future<JournalEntryValidationResult> validateJournalEntry(CreateJournalEntryRequest request) async {
    try {
      final response = await _apiService.post(
        '$_baseUrl/journalentries/validate',
        body: json.encode(request.toJson()),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return JournalEntryValidationResult.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في التحقق من القيد');
      }
    } catch (e) {
      throw Exception('خطأ في التحقق من القيد: $e');
    }
  }

  /// تقرير ميزان المراجعة
  Future<TrialBalanceReport> getTrialBalance({DateTime? asOfDate}) async {
    try {
      String url = '$_baseUrl/journalentries/trial-balance';
      if (asOfDate != null) {
        url += '?asOfDate=${asOfDate.toIso8601String()}';
      }

      final response = await _apiService.get(url);
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return TrialBalanceReport.fromJson(data);
      } else {
        throw Exception('فشل في إنتاج تقرير ميزان المراجعة: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إنتاج تقرير ميزان المراجعة: $e');
    }
  }
}

/// نموذج نتيجة التحقق من القيد
/// Journal Entry Validation Result Model
class JournalEntryValidationResult {
  final bool isBalanced;
  final double totalDebit;
  final double totalCredit;
  final double difference;
  final String message;

  JournalEntryValidationResult({
    required this.isBalanced,
    required this.totalDebit,
    required this.totalCredit,
    required this.difference,
    required this.message,
  });

  factory JournalEntryValidationResult.fromJson(Map<String, dynamic> json) {
    return JournalEntryValidationResult(
      isBalanced: json['isBalanced'] ?? false,
      totalDebit: (json['totalDebit'] ?? 0).toDouble(),
      totalCredit: (json['totalCredit'] ?? 0).toDouble(),
      difference: (json['difference'] ?? 0).toDouble(),
      message: json['message'] ?? '',
    );
  }
}

/// نموذج تقرير ميزان المراجعة
/// Trial Balance Report Model
class TrialBalanceReport {
  final DateTime asOfDate;
  final List<TrialBalanceItem> items;
  final double totalDebits;
  final double totalCredits;

  TrialBalanceReport({
    required this.asOfDate,
    required this.items,
    required this.totalDebits,
    required this.totalCredits,
  });

  factory TrialBalanceReport.fromJson(Map<String, dynamic> json) {
    return TrialBalanceReport(
      asOfDate: DateTime.parse(json['asOfDate']),
      items: (json['items'] as List)
          .map((item) => TrialBalanceItem.fromJson(item))
          .toList(),
      totalDebits: (json['totalDebits'] ?? 0).toDouble(),
      totalCredits: (json['totalCredits'] ?? 0).toDouble(),
    );
  }

  bool get isBalanced => (totalDebits - totalCredits).abs() < 0.01;
}

/// نموذج عنصر ميزان المراجعة
/// Trial Balance Item Model
class TrialBalanceItem {
  final int accountId;
  final String accountCode;
  final String accountName;
  final double debitBalance;
  final double creditBalance;

  TrialBalanceItem({
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    required this.debitBalance,
    required this.creditBalance,
  });

  factory TrialBalanceItem.fromJson(Map<String, dynamic> json) {
    return TrialBalanceItem(
      accountId: json['accountId'] ?? 0,
      accountCode: json['accountCode'] ?? '',
      accountName: json['accountName'] ?? '',
      debitBalance: (json['debitBalance'] ?? 0).toDouble(),
      creditBalance: (json['creditBalance'] ?? 0).toDouble(),
    );
  }
}
