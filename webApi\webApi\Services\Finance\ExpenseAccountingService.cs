using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;
using System.Threading.Tasks;
using webApi.DTOs.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// خدمة ربط المصروفات بالنظام المحاسبي
    /// </summary>
    public interface IExpenseAccountingService
    {
        Task<JournalEntry> GenerateExpenseJournalEntryAsync(int expenseId);
        Task<bool> ProcessExpenseApprovalAsync(int expenseId, int approvedBy);
        Task<bool> ProcessExpensePaymentAsync(int expenseId, int fundId, string paymentMethod);
        Task<bool> ReverseExpenseJournalEntryAsync(int expenseId, string reason);
        Task<List<JournalEntry>> GetExpenseJournalEntriesAsync(int expenseId);
        Task<decimal> GetExpenseBudgetImpactAsync(int expenseId);
    }

    public class ExpenseAccountingService : IExpenseAccountingService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<ExpenseAccountingService> _logger;
        private readonly ILoggingService _loggingService;
        private readonly IAccountingService _accountingService;
        private readonly IFundManagementService _fundManagementService;

        public ExpenseAccountingService(
            TasksDbContext context,
            ILogger<ExpenseAccountingService> logger,
            ILoggingService loggingService,
            IAccountingService accountingService,
            IFundManagementService fundManagementService)
        {
            _context = context;
            _logger = logger;
            _loggingService = loggingService;
            _accountingService = accountingService;
            _fundManagementService = fundManagementService;
        }

        /// <summary>
        /// توليد قيد محاسبي للمصروف
        /// </summary>
        public async Task<JournalEntry> GenerateExpenseJournalEntryAsync(int expenseId)
        {
            try
            {
                var expense = await _context.Expenses
                    .Include(e => e.ExpenseLines)
                        .ThenInclude(l => l.Account)
                    .Include(e => e.ExpenseItemType)
                    .Include(e => e.Requester)
                    .Include(e => e.Department)
                    .FirstOrDefaultAsync(e => e.Id == expenseId);

                if (expense == null)
                    throw new ArgumentException($"المصروف غير موجود: {expenseId}");

                if (expense.Status != "approved")
                    throw new InvalidOperationException("لا يمكن توليد قيد محاسبي لمصروف غير معتمد");

                // التحقق من عدم وجود قيد محاسبي مسبق
                var existingEntry = await _context.JournalEntries
                    .FirstOrDefaultAsync(j => j.ReferenceType == "expense" && j.ReferenceId == expenseId);

                if (existingEntry != null)
                    throw new InvalidOperationException("يوجد قيد محاسبي مسجل لهذا المصروف مسبقاً");

                // إنشاء القيد المحاسبي
                var journalEntryRequest = new CreateJournalEntryRequest
                {
                    Description = $"قيد مصروف: {expense.Title}",
                    ReferenceType = "expense",
                    ReferenceId = expenseId,
                    ReferenceNumber = expense.ExpenseNumber,
                    EntryDate = expense.ExpenseDate,
                    Lines = new List<CreateJournalEntryLineRequest>()
                };

                // إضافة خطوط القيد من خطوط المصروف
                foreach (var line in expense.ExpenseLines)
                {
                    // خط مدين (المصروف)
                    journalEntryRequest.Lines.Add(new CreateJournalEntryLineRequest
                    {
                        AccountId = line.AccountId,
                        DebitAmount = line.Amount,
                        CreditAmount = 0,
                        Description = line.Description ?? $"مصروف: {expense.Title}",
                        CostCenterId = line.DepartmentId,
                        ProjectId = line.TaskId
                    });
                }

                // خط دائن (الصندوق أو الحساب الدائن)
                var payableAccountId = await GetAccountsPayableAccountIdAsync();
                journalEntryRequest.Lines.Add(new CreateJournalEntryLineRequest
                {
                    AccountId = payableAccountId,
                    DebitAmount = 0,
                    CreditAmount = expense.TotalAmount,
                    Description = $"مستحق دفع - {expense.Title}",
                    CostCenterId = expense.DepartmentId,
                    ProjectId = expense.TaskId
                });

                // إنشاء القيد
                var journalEntry = await _accountingService.CreateJournalEntryAsync(journalEntryRequest);

                // ربط القيد بالمصروف
                expense.JournalEntryId = journalEntry.Id;
                expense.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "توليد قيد محاسبي للمصروف",
                    "Expense",
                    expenseId,
                    expense.RequestedBy,
                    $"تم توليد قيد محاسبي رقم {journalEntry.Id} للمصروف {expense.ExpenseNumber} بمبلغ {expense.TotalAmount:C}"
                );

                _logger.LogInformation("تم توليد قيد محاسبي للمصروف {ExpenseNumber}: قيد رقم {JournalEntryId}",
                    expense.ExpenseNumber, journalEntry.Id);

                return journalEntry;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد قيد محاسبي للمصروف {ExpenseId}", expenseId);
                throw;
            }
        }

        /// <summary>
        /// معالجة اعتماد المصروف مع توليد القيد المحاسبي
        /// </summary>
        public async Task<bool> ProcessExpenseApprovalAsync(int expenseId, int approvedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var expense = await _context.Expenses.FindAsync(expenseId);
                if (expense == null)
                    throw new ArgumentException($"المصروف غير موجود: {expenseId}");

                if (expense.Status != "submitted")
                    throw new InvalidOperationException("لا يمكن اعتماد مصروف غير مقدم للاعتماد");

                // تحديث حالة المصروف
                expense.Status = "approved";
                expense.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // إضافة سجل اعتماد
                var approval = new ExpenseApproval
                {
                    ExpenseId = expenseId,
                    ApproverId = approvedBy,
                    Level = 1, // TODO: تطوير نظام مستويات الاعتماد
                    LevelName = "الاعتماد الأولي",
                    Status = "approved",
                    ApprovedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    Comments = "تم الاعتماد تلقائياً",
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.ExpenseApprovals.Add(approval);
                await _context.SaveChangesAsync();

                // توليد القيد المحاسبي
                var journalEntry = await GenerateExpenseJournalEntryAsync(expenseId);

                // التحقق من تأثير الميزانية
                await CheckBudgetImpactAsync(expenseId);

                await transaction.CommitAsync();

                _logger.LogInformation("تم اعتماد المصروف {ExpenseId} وتوليد القيد المحاسبي {JournalEntryId}",
                    expenseId, journalEntry.Id);

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في معالجة اعتماد المصروف {ExpenseId}", expenseId);
                throw;
            }
        }

        /// <summary>
        /// معالجة دفع المصروف مع تحديث الصندوق
        /// </summary>
        public async Task<bool> ProcessExpensePaymentAsync(int expenseId, int fundId, string paymentMethod)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var expense = await _context.Expenses.FindAsync(expenseId);
                if (expense == null)
                    throw new ArgumentException($"المصروف غير موجود: {expenseId}");

                if (expense.Status != "approved")
                    throw new InvalidOperationException("لا يمكن دفع مصروف غير معتمد");

                // التحقق من رصيد الصندوق
                var fund = await _context.Funds.FindAsync(fundId);
                if (fund == null)
                    throw new ArgumentException($"الصندوق غير موجود: {fundId}");

                if (fund.CurrentBalance < expense.TotalAmount)
                    throw new InvalidOperationException($"رصيد الصندوق غير كافي. الرصيد الحالي: {fund.CurrentBalance:C}، المطلوب: {expense.TotalAmount:C}");

                // تحديث حالة المصروف
                expense.Status = "paid";
                expense.PaymentDate = DateTime.UtcNow;
                expense.PaymentMethod = paymentMethod;
                expense.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // إضافة حركة للصندوق
                var fundTransaction = await _fundManagementService.AddFundTransactionAsync(new CreateFundTransactionRequest
                {
                    FundId = fundId,
                    TransactionType = "withdrawal",
                    Amount = expense.TotalAmount,
                    Description = $"دفع مصروف: {expense.Title}",
                    ReferenceNumber = expense.ExpenseNumber,
                    ReferenceType = "expense",
                    TransactionDate = DateTime.UtcNow,
                    CreatedBy = expense.RequestedBy
                });

                // توليد قيد دفع إضافي
                await GeneratePaymentJournalEntryAsync(expenseId, fundId);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("تم دفع المصروف {ExpenseId} من الصندوق {FundId} بمبلغ {Amount:C}",
                    expenseId, fundId, expense.TotalAmount);

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في معالجة دفع المصروف {ExpenseId}", expenseId);
                throw;
            }
        }

        /// <summary>
        /// عكس القيد المحاسبي للمصروف
        /// </summary>
        public async Task<bool> ReverseExpenseJournalEntryAsync(int expenseId, string reason)
        {
            try
            {
                var expense = await _context.Expenses.FindAsync(expenseId);
                if (expense == null)
                    throw new ArgumentException($"المصروف غير موجود: {expenseId}");

                if (!expense.JournalEntryId.HasValue)
                    throw new InvalidOperationException("لا يوجد قيد محاسبي لهذا المصروف");

                // إلغاء القيد المحاسبي
                var cancelled = await _accountingService.CancelJournalEntryAsync(expense.JournalEntryId.Value, 1); // TODO: الحصول على معرف المستخدم الحالي

                // تحديث حالة المصروف
                expense.Status = "cancelled";
                expense.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إلغاء القيد المحاسبي للمصروف {ExpenseId}",
                    expenseId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عكس القيد المحاسبي للمصروف {ExpenseId}", expenseId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على القيود المحاسبية للمصروف
        /// </summary>
        public async Task<List<JournalEntry>> GetExpenseJournalEntriesAsync(int expenseId)
        {
            try
            {
                var expense = await _context.Expenses.FindAsync(expenseId);
                if (expense == null)
                    throw new ArgumentException($"المصروف غير موجود: {expenseId}");

                var journalEntries = await _context.JournalEntries
                    .Include(j => j.Lines)
                        .ThenInclude(l => l.Account)
                    .Where(j => (j.ReferenceType == "expense" && j.ReferenceId == expenseId) ||
                               (j.ReferenceType == "expense_payment" && j.ReferenceId == expenseId))
                    .OrderBy(j => j.EntryDate)
                    .ToListAsync();

                return journalEntries;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على القيود المحاسبية للمصروف {ExpenseId}", expenseId);
                throw;
            }
        }

        /// <summary>
        /// حساب تأثير المصروف على الميزانية
        /// </summary>
        public async Task<decimal> GetExpenseBudgetImpactAsync(int expenseId)
        {
            try
            {
                var expense = await _context.Expenses
                    .Include(e => e.ExpenseLines)
                    .FirstOrDefaultAsync(e => e.Id == expenseId);

                if (expense == null)
                    return 0;

                // حساب التأثير على الميزانية حسب الحسابات والأقسام
                decimal totalImpact = 0;

                foreach (var line in expense.ExpenseLines)
                {
                    // البحث عن ميزانية مطابقة
                    var budgetLine = await _context.BudgetLines
                        .FirstOrDefaultAsync(bl => bl.AccountId == line.AccountId &&
                                                  bl.TaskId == line.TaskId);

                    if (budgetLine != null)
                    {
                        totalImpact += line.Amount;
                    }
                }

                return totalImpact;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تأثير المصروف على الميزانية {ExpenseId}", expenseId);
                return 0;
            }
        }

        // ===== الدوال المساعدة =====

        private async Task<int> GetAccountsPayableAccountIdAsync()
        {
            // البحث عن حساب الدائنون
            var account = await _context.Accounts
                .FirstOrDefaultAsync(a => a.AccountCode == "2100" || a.AccountName.Contains("دائنون"));

            if (account == null)
            {
                // إنشاء حساب الدائنون إذا لم يكن موجوداً
                account = new Account
                {
                    AccountCode = "2100",
                    AccountName = "الدائنون",
                    AccountType = "liability",
                    BalanceType = "credit",
                    IsActive = true,
                    IsSystemAccount = true,
                    CreatedBy = 1, // TODO: استخدام معرف المستخدم الحالي
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Accounts.Add(account);
                await _context.SaveChangesAsync();
            }

            return account.Id;
        }

        private async System.Threading.Tasks.Task GeneratePaymentJournalEntryAsync(int expenseId, int fundId)
        {
            var expense = await _context.Expenses.FindAsync(expenseId);
            var fund = await _context.Funds.Include(f => f.Account).FirstOrDefaultAsync(f => f.Id == fundId);

            if (expense == null || fund == null)
                return;

            var payableAccountId = await GetAccountsPayableAccountIdAsync();

            var paymentEntryRequest = new CreateJournalEntryRequest
            {
                Description = $"دفع مصروف: {expense.Title}",
                ReferenceType = "expense_payment",
                ReferenceId = expenseId,
                ReferenceNumber = expense.ExpenseNumber,
                EntryDate = expense.PaymentDate ?? DateTime.UtcNow,
                Lines = new List<CreateJournalEntryLineRequest>
                {
                    // خط مدين (تسديد الدائنون)
                    new CreateJournalEntryLineRequest
                    {
                        AccountId = payableAccountId,
                        DebitAmount = expense.TotalAmount,
                        CreditAmount = 0,
                        Description = $"تسديد مستحق - {expense.Title}"
                    },
                    // خط دائن (الصندوق)
                    new CreateJournalEntryLineRequest
                    {
                        AccountId = fund.AccountId,
                        DebitAmount = 0,
                        CreditAmount = expense.TotalAmount,
                        Description = $"دفع من {fund.FundName}"
                    }
                }
            };

            await _accountingService.CreateJournalEntryAsync(paymentEntryRequest);
        }

        private async System.Threading.Tasks.Task CheckBudgetImpactAsync(int expenseId)
        {
            var budgetImpact = await GetExpenseBudgetImpactAsync(expenseId);
            
            if (budgetImpact > 0)
            {
                // TODO: تطوير منطق التحقق من تجاوز الميزانية
                _logger.LogInformation("تأثير المصروف {ExpenseId} على الميزانية: {BudgetImpact:C}", expenseId, budgetImpact);
            }
        }
    }
}
