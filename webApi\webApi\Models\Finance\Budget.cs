using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج الميزانية
    /// Budget Model
    /// </summary>
    [Table("budgets")]
    public class Budget
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Column("budget_name")]
        public string BudgetName { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        [Column("fiscal_year")]
        public string FiscalYear { get; set; } = string.Empty;

        [Required]
        [Column("start_date")]
        public DateTime StartDate { get; set; }

        [Required]
        [Column("end_date")]
        public DateTime EndDate { get; set; }

        [Required]
        [StringLength(20)]
        [Column("status")]
        public string Status { get; set; } = "draft"; // draft, active, closed

        [Column("total_amount", TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [Column("spent_amount", TypeName = "decimal(18,2)")]
        public decimal SpentAmount { get; set; } = 0;

        [Column("committed_amount", TypeName = "decimal(18,2)")]
        public decimal CommittedAmount { get; set; } = 0;

        [Column("is_control_enabled")]
        public bool IsControlEnabled { get; set; } = false;

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("approved_by")]
        public int? ApprovedBy { get; set; }

        [Column("approved_at")]
        public long? ApprovedAt { get; set; }

        [Column("updated_at")]
        public long? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        public virtual ICollection<BudgetLine> BudgetLines { get; set; } = new List<BudgetLine>();

        // Calculated Properties
        [NotMapped]
        public decimal RemainingAmount => TotalAmount - SpentAmount - CommittedAmount;

        [NotMapped]
        public decimal UtilizationPercentage => TotalAmount > 0 ? (SpentAmount / TotalAmount) * 100 : 0;

        [NotMapped]
        public bool IsActive => Status == "active";

        [NotMapped]
        public bool IsDraft => Status == "draft";

        [NotMapped]
        public bool IsClosed => Status == "closed";

        [NotMapped]
        public string StatusText => Status switch
        {
            "draft" => "مسودة",
            "active" => "نشطة",
            "closed" => "مغلقة",
            _ => "غير محدد"
        };

        [NotMapped]
        public bool IsOverBudget => SpentAmount > TotalAmount;

        [NotMapped]
        public bool IsNearLimit => UtilizationPercentage >= 90;
    }
}
