using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج مرفق المصروف
    /// Expense Attachment Model
    /// </summary>
    [Table("expense_attachments")]
    public class ExpenseAttachment
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("expense_id")]
        public int ExpenseId { get; set; }

        [Required]
        [StringLength(255)]
        [Column("file_name")]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Column("original_name")]
        public string OriginalName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        [Column("file_path")]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Column("content_type")]
        public string ContentType { get; set; } = string.Empty;

        [Required]
        [Column("file_size")]
        public long FileSize { get; set; }

        [StringLength(200)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("uploaded_by")]
        public int UploadedBy { get; set; }

        [Required]
        [Column("uploaded_at")]
        public long UploadedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ExpenseId")]
        public virtual Expense Expense { get; set; } = null!;

        [ForeignKey("UploadedBy")]
        public virtual User Uploader { get; set; } = null!;

        // Calculated Properties
        [NotMapped]
        public DateTime UploadedDateTime => DateTimeOffset.FromUnixTimeSeconds(UploadedAt).DateTime;

        [NotMapped]
        public string FileSizeFormatted
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} B";
                else if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024:F1} KB";
                else if (FileSize < 1024 * 1024 * 1024)
                    return $"{FileSize / (1024 * 1024):F1} MB";
                else
                    return $"{FileSize / (1024 * 1024 * 1024):F1} GB";
            }
        }

        [NotMapped]
        public string FileExtension => Path.GetExtension(OriginalName).ToLowerInvariant();

        [NotMapped]
        public bool IsImage => new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" }
            .Contains(FileExtension);

        [NotMapped]
        public bool IsPdf => FileExtension == ".pdf";

        [NotMapped]
        public bool IsDocument => new[] { ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt" }
            .Contains(FileExtension);

        [NotMapped]
        public string FileTypeCategory
        {
            get
            {
                if (IsImage) return "image";
                if (IsPdf) return "pdf";
                if (IsDocument) return "document";
                return "other";
            }
        }
    }
}
