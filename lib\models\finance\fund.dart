import 'account.dart';

/// نموذج الصندوق
/// Fund Model
class Fund {
  final int id;
  final String fundName;
  final String fundCode;
  final int accountId;
  final int? locationId;
  final int? legalEntityId;
  final double currentBalance;
  final double openingBalance;
  final String fundType;
  final String? bankName;
  final String? accountNumber;
  final bool isActive;
  final bool isDefault;
  final String? description;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;

  // Navigation Properties
  final Account? account;
  final User? creator;

  Fund({
    required this.id,
    required this.fundName,
    required this.fundCode,
    required this.accountId,
    this.locationId,
    this.legalEntityId,
    required this.currentBalance,
    required this.openingBalance,
    required this.fundType,
    this.bankName,
    this.accountNumber,
    required this.isActive,
    required this.isDefault,
    this.description,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.account,
    this.creator,
  });

  factory Fund.fromJson(Map<String, dynamic> json) {
    return Fund(
      id: json['id'] ?? 0,
      fundName: json['fund_name'] ?? '',
      fundCode: json['fund_code'] ?? '',
      accountId: json['account_id'] ?? 0,
      locationId: json['location_id'],
      legalEntityId: json['legal_entity_id'],
      currentBalance: (json['current_balance'] ?? 0).toDouble(),
      openingBalance: (json['opening_balance'] ?? 0).toDouble(),
      fundType: json['fund_type'] ?? 'cash',
      bankName: json['bank_name'],
      accountNumber: json['account_number'],
      isActive: json['is_active'] ?? true,
      isDefault: json['is_default'] ?? false,
      description: json['description'],
      createdBy: json['created_by'] ?? 0,
      createdAt: json['created_at'] ?? 0,
      updatedAt: json['updated_at'],
      account: json['account'] != null
          ? Account.fromJson(json['account'])
          : null,
      creator: json['creator'] != null
          ? User.fromJson(json['creator'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fund_name': fundName,
      'fund_code': fundCode,
      'account_id': accountId,
      'location_id': locationId,
      'legal_entity_id': legalEntityId,
      'current_balance': currentBalance,
      'opening_balance': openingBalance,
      'fund_type': fundType,
      'bank_name': bankName,
      'account_number': accountNumber,
      'is_active': isActive,
      'is_default': isDefault,
      'description': description,
      'created_by': createdBy,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // Calculated Properties
  String get displayName => '$fundCode - $fundName';

  String get fundTypeText {
    switch (fundType) {
      case 'cash':
        return 'نقدي';
      case 'bank':
        return 'بنكي';
      case 'petty_cash':
        return 'نثرية';
      default:
        return 'غير محدد';
    }
  }

  bool get isCashFund => fundType == 'cash';
  bool get isBankFund => fundType == 'bank';
  bool get isPettyCashFund => fundType == 'petty_cash';

  Fund copyWith({
    int? id,
    String? fundName,
    String? fundCode,
    int? accountId,
    int? locationId,
    int? legalEntityId,
    double? currentBalance,
    double? openingBalance,
    String? fundType,
    String? bankName,
    String? accountNumber,
    bool? isActive,
    bool? isDefault,
    String? description,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    Account? account,
    User? creator,
  }) {
    return Fund(
      id: id ?? this.id,
      fundName: fundName ?? this.fundName,
      fundCode: fundCode ?? this.fundCode,
      accountId: accountId ?? this.accountId,
      locationId: locationId ?? this.locationId,
      legalEntityId: legalEntityId ?? this.legalEntityId,
      currentBalance: currentBalance ?? this.currentBalance,
      openingBalance: openingBalance ?? this.openingBalance,
      fundType: fundType ?? this.fundType,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      description: description ?? this.description,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      account: account ?? this.account,
      creator: creator ?? this.creator,
    );
  }

  @override
  String toString() {
    return 'Fund{id: $id, fundCode: $fundCode, fundName: $fundName}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Fund && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج حركة الصندوق
/// Fund Transaction Model
class FundTransaction {
  final int id;
  final int fundId;
  final String transactionType;
  final double amount;
  final double balanceAfter;
  final String? referenceNumber;
  final String? referenceType;
  final int? referenceId;
  final String? description;
  final int? journalEntryId;
  final DateTime transactionDate;
  final int createdBy;
  final int createdAt;

  // Navigation Properties
  final Fund? fund;
  final User? creator;

  FundTransaction({
    required this.id,
    required this.fundId,
    required this.transactionType,
    required this.amount,
    required this.balanceAfter,
    this.referenceNumber,
    this.referenceType,
    this.referenceId,
    this.description,
    this.journalEntryId,
    required this.transactionDate,
    required this.createdBy,
    required this.createdAt,
    this.fund,
    this.creator,
  });

  factory FundTransaction.fromJson(Map<String, dynamic> json) {
    return FundTransaction(
      id: json['id'] ?? 0,
      fundId: json['fund_id'] ?? 0,
      transactionType: json['transaction_type'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      balanceAfter: (json['balance_after'] ?? 0).toDouble(),
      referenceNumber: json['reference_number'],
      referenceType: json['reference_type'],
      referenceId: json['reference_id'],
      description: json['description'],
      journalEntryId: json['journal_entry_id'],
      transactionDate: DateTime.parse(json['transaction_date']),
      createdBy: json['created_by'] ?? 0,
      createdAt: json['created_at'] ?? 0,
      fund: json['fund'] != null
          ? Fund.fromJson(json['fund'])
          : null,
      creator: json['creator'] != null
          ? User.fromJson(json['creator'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fund_id': fundId,
      'transaction_type': transactionType,
      'amount': amount,
      'balance_after': balanceAfter,
      'reference_number': referenceNumber,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'description': description,
      'journal_entry_id': journalEntryId,
      'transaction_date': transactionDate.toIso8601String(),
      'created_by': createdBy,
      'created_at': createdAt,
    };
  }

  // Calculated Properties
  bool get isDeposit => transactionType == 'deposit' || transactionType == 'transfer_in';
  bool get isWithdrawal => transactionType == 'withdrawal' || transactionType == 'transfer_out';

  String get transactionTypeText {
    switch (transactionType) {
      case 'deposit':
        return 'إيداع';
      case 'withdrawal':
        return 'سحب';
      case 'transfer_in':
        return 'تحويل وارد';
      case 'transfer_out':
        return 'تحويل صادر';
      default:
        return 'غير محدد';
    }
  }

  String get amountDisplay => isDeposit ? '+${amount.toStringAsFixed(2)}' : '-${amount.toStringAsFixed(2)}';
}

/// نماذج الطلبات
/// Request Models

class CreateFundRequest {
  final String fundName;
  final String fundCode;
  final int accountId;
  final int? locationId;
  final int? legalEntityId;
  final double openingBalance;
  final String fundType;
  final String? bankName;
  final String? accountNumber;
  final String? description;

  CreateFundRequest({
    required this.fundName,
    required this.fundCode,
    required this.accountId,
    this.locationId,
    this.legalEntityId,
    required this.openingBalance,
    required this.fundType,
    this.bankName,
    this.accountNumber,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'fund_name': fundName,
      'fund_code': fundCode,
      'account_id': accountId,
      'location_id': locationId,
      'legal_entity_id': legalEntityId,
      'opening_balance': openingBalance,
      'fund_type': fundType,
      'bank_name': bankName,
      'account_number': accountNumber,
      'description': description,
    };
  }
}

class CreateFundTransactionRequest {
  final String transactionType;
  final double amount;
  final String? referenceNumber;
  final String? referenceType;
  final int? referenceId;
  final String? description;
  final DateTime transactionDate;

  CreateFundTransactionRequest({
    required this.transactionType,
    required this.amount,
    this.referenceNumber,
    this.referenceType,
    this.referenceId,
    this.description,
    required this.transactionDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'transaction_type': transactionType,
      'amount': amount,
      'reference_number': referenceNumber,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'description': description,
      'transaction_date': transactionDate.toIso8601String(),
    };
  }
}

class FundTransferRequest {
  final int sourceFundId;
  final int targetFundId;
  final double amount;
  final String? description;
  final String? referenceNumber;

  FundTransferRequest({
    required this.sourceFundId,
    required this.targetFundId,
    required this.amount,
    this.description,
    this.referenceNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'source_fund_id': sourceFundId,
      'target_fund_id': targetFundId,
      'amount': amount,
      'description': description,
      'reference_number': referenceNumber,
    };
  }
}

/// نماذج الفلاتر
/// Filter Models

class FundTransactionFilter {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? transactionType;
  final String? referenceType;
  final int pageNumber;
  final int pageSize;

  FundTransactionFilter({
    this.fromDate,
    this.toDate,
    this.transactionType,
    this.referenceType,
    this.pageNumber = 1,
    this.pageSize = 50,
  });

  Map<String, String> toQueryParameters() {
    final Map<String, String> params = {};
    
    if (fromDate != null) {
      params['fromDate'] = fromDate!.toIso8601String();
    }
    if (toDate != null) {
      params['toDate'] = toDate!.toIso8601String();
    }
    if (transactionType != null && transactionType!.isNotEmpty) {
      params['transactionType'] = transactionType!;
    }
    if (referenceType != null && referenceType!.isNotEmpty) {
      params['referenceType'] = referenceType!;
    }
    params['pageNumber'] = pageNumber.toString();
    params['pageSize'] = pageSize.toString();

    return params;
  }
}
