using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;
using webApi.DTOs.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// خدمة التقارير المالية الأساسية
    /// </summary>
    public interface IFinancialReportingService
    {
        Task<BalanceSheetReport> GenerateBalanceSheetAsync(DateTime asOfDate, int? departmentId = null);
        Task<IncomeStatementReport> GenerateIncomeStatementAsync(DateTime startDate, DateTime endDate, int? departmentId = null);
        Task<CashFlowReport> GenerateCashFlowReportAsync(DateTime startDate, DateTime endDate, int? fundId = null);
        Task<DTOs.Finance.FundBalanceReport> GenerateFundBalanceReportAsync(DateTime asOfDate, int? fundId = null);
        Task<ExpenseReport> GenerateExpenseReportAsync(DateTime startDate, DateTime endDate, int? departmentId = null);
        Task<PayrollReport> GeneratePayrollReportAsync(DateTime startDate, DateTime endDate, int? departmentId = null);
        Task<BudgetVsActualReport> GenerateBudgetVsActualReportAsync(DateTime startDate, DateTime endDate, int? departmentId = null);
        Task<AccountBalanceReport> GenerateAccountBalanceReportAsync(DateTime asOfDate, string? accountType = null);
    }

    public class FinancialReportingService : IFinancialReportingService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<FinancialReportingService> _logger;

        public FinancialReportingService(
            TasksDbContext context,
            ILogger<FinancialReportingService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// تقرير الميزانية العمومية
        /// </summary>
        public async Task<BalanceSheetReport> GenerateBalanceSheetAsync(DateTime asOfDate, int? departmentId = null)
        {
            try
            {
                var report = new BalanceSheetReport
                {
                    AsOfDate = asOfDate,
                    DepartmentId = departmentId,
                    GeneratedAt = DateTime.UtcNow
                };

                // الأصول
                report.Assets = await GetAccountBalancesByTypeAsync("asset", asOfDate, departmentId);
                report.TotalAssets = report.Assets.Sum(a => a.Balance);

                // الخصوم
                report.Liabilities = await GetAccountBalancesByTypeAsync("liability", asOfDate, departmentId);
                report.TotalLiabilities = report.Liabilities.Sum(l => l.Balance);

                // حقوق الملكية
                report.Equity = await GetAccountBalancesByTypeAsync("equity", asOfDate, departmentId);
                report.TotalEquity = report.Equity.Sum(e => e.Balance);

                // التحقق من توازن الميزانية
                report.IsBalanced = Math.Abs(report.TotalAssets - (report.TotalLiabilities + report.TotalEquity)) < 0.01m;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير الميزانية العمومية");
                throw;
            }
        }

        /// <summary>
        /// تقرير قائمة الدخل
        /// </summary>
        public async Task<IncomeStatementReport> GenerateIncomeStatementAsync(DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            try
            {
                var report = new IncomeStatementReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    DepartmentId = departmentId,
                    GeneratedAt = DateTime.UtcNow
                };

                // الإيرادات
                report.Revenues = await GetAccountBalancesByTypeForPeriodAsync("revenue", startDate, endDate, departmentId);
                report.TotalRevenues = report.Revenues.Sum(r => r.Balance);

                // المصروفات
                report.Expenses = await GetAccountBalancesByTypeForPeriodAsync("expense", startDate, endDate, departmentId);
                report.TotalExpenses = report.Expenses.Sum(e => e.Balance);

                // صافي الدخل
                report.NetIncome = report.TotalRevenues - report.TotalExpenses;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير قائمة الدخل");
                throw;
            }
        }

        /// <summary>
        /// تقرير التدفقات النقدية
        /// </summary>
        public async Task<CashFlowReport> GenerateCashFlowReportAsync(DateTime startDate, DateTime endDate, int? fundId = null)
        {
            try
            {
                var report = new CashFlowReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    FundId = fundId,
                    GeneratedAt = DateTime.UtcNow
                };

                var query = _context.FundTransactions
                    .Include(ft => ft.Fund)
                    .Where(ft => ft.TransactionDate.Date >= startDate.Date && 
                                ft.TransactionDate.Date <= endDate.Date);

                if (fundId.HasValue)
                {
                    query = query.Where(ft => ft.FundId == fundId.Value);
                }

                var transactions = await query.OrderBy(ft => ft.TransactionDate).ToListAsync();

                // تجميع المعاملات حسب النوع
                report.CashInflows = transactions
                    .Where(t => t.TransactionType == "deposit" || t.TransactionType == "transfer_in")
                    .GroupBy(t => t.Description)
                    .Select(g => new CashFlowItem
                    {
                        Description = g.Key ?? "غير محدد",
                        Amount = g.Sum(t => t.Amount),
                        TransactionCount = g.Count()
                    })
                    .ToList();

                report.CashOutflows = transactions
                    .Where(t => t.TransactionType == "withdrawal" || t.TransactionType == "transfer_out")
                    .GroupBy(t => t.Description)
                    .Select(g => new CashFlowItem
                    {
                        Description = g.Key ?? "غير محدد",
                        Amount = g.Sum(t => t.Amount),
                        TransactionCount = g.Count()
                    })
                    .ToList();

                report.TotalInflows = report.CashInflows.Sum(i => i.Amount);
                report.TotalOutflows = report.CashOutflows.Sum(o => o.Amount);
                report.NetCashFlow = report.TotalInflows - report.TotalOutflows;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير التدفقات النقدية");
                throw;
            }
        }

        /// <summary>
        /// تقرير أرصدة الصناديق
        /// </summary>
        public async Task<DTOs.Finance.FundBalanceReport> GenerateFundBalanceReportAsync(DateTime asOfDate, int? fundId = null)
        {
            try
            {
                var query = _context.Funds
                    .Include(f => f.Account)
                    .Where(f => f.IsActive);

                if (fundId.HasValue)
                {
                    query = query.Where(f => f.Id == fundId.Value);
                }

                var funds = await query.ToListAsync();

                var report = new DTOs.Finance.FundBalanceReport
                {
                    AsOfDate = asOfDate,
                    GeneratedAt = DateTime.UtcNow,
                    FundBalances = new List<FundBalanceItem>()
                };

                foreach (var fund in funds)
                {
                    var balance = await CalculateFundBalanceAsOfDateAsync(fund.Id, asOfDate);
                    
                    report.FundBalances.Add(new FundBalanceItem
                    {
                        FundId = fund.Id,
                        FundName = fund.FundName,
                        FundCode = fund.FundCode,
                        AccountName = fund.Account?.AccountName,
                        OpeningBalance = fund.OpeningBalance,
                        CurrentBalance = balance,
                        Currency = "SAR" // إزالة fund.Currency لأنه غير موجود
                    });
                }

                report.TotalBalance = report.FundBalances.Sum(f => f.CurrentBalance);

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير أرصدة الصناديق");
                throw;
            }
        }

        /// <summary>
        /// تقرير المصروفات
        /// </summary>
        public async Task<ExpenseReport> GenerateExpenseReportAsync(DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            try
            {
                var query = _context.Expenses
                    .Include(e => e.ExpenseItemType)
                    .Include(e => e.Department)
                    .Include(e => e.Requester)
                    .Include(e => e.ExpenseLines)
                        .ThenInclude(l => l.Account)
                    .Where(e => e.ExpenseDate.Date >= startDate.Date && 
                               e.ExpenseDate.Date <= endDate.Date);

                if (departmentId.HasValue)
                {
                    query = query.Where(e => e.DepartmentId == departmentId.Value);
                }

                var expenses = await query.ToListAsync();

                var report = new ExpenseReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    DepartmentId = departmentId,
                    GeneratedAt = DateTime.UtcNow
                };

                // تجميع المصروفات حسب النوع
                report.ExpensesByType = expenses
                    .GroupBy(e => e.ExpenseItemType?.Name ?? "غير محدد")
                    .Select(g => new ExpenseByTypeItem
                    {
                        ExpenseType = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(e => e.TotalAmount),
                        AverageAmount = g.Average(e => e.TotalAmount)
                    })
                    .OrderByDescending(x => x.TotalAmount)
                    .ToList();

                // تجميع المصروفات حسب القسم
                report.ExpensesByDepartment = expenses
                    .GroupBy(e => e.Department?.Name ?? "غير محدد")
                    .Select(g => new ExpenseByDepartmentItem
                    {
                        DepartmentName = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(e => e.TotalAmount),
                        AverageAmount = g.Average(e => e.TotalAmount)
                    })
                    .OrderByDescending(x => x.TotalAmount)
                    .ToList();

                // تجميع المصروفات حسب الحالة
                report.ExpensesByStatus = expenses
                    .GroupBy(e => e.Status)
                    .Select(g => new ExpenseByStatusItem
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(e => e.TotalAmount)
                    })
                    .ToList();

                report.TotalExpenses = expenses.Sum(e => e.TotalAmount);
                report.TotalCount = expenses.Count;
                report.AverageExpense = expenses.Any() ? expenses.Average(e => e.TotalAmount) : 0;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير المصروفات");
                throw;
            }
        }

        /// <summary>
        /// تقرير الرواتب
        /// </summary>
        public async Task<PayrollReport> GeneratePayrollReportAsync(DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            try
            {
                var query = _context.EmployeePayrolls
                    .Include(p => p.Employee)
                        .ThenInclude(e => e.Department)
                    .Where(p => p.PayPeriodStart.Date >= startDate.Date && 
                               p.PayPeriodEnd.Date <= endDate.Date);

                if (departmentId.HasValue)
                {
                    query = query.Where(p => p.Employee.DepartmentId == departmentId.Value);
                }

                var payrolls = await query.ToListAsync();

                var report = new PayrollReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    DepartmentId = departmentId,
                    GeneratedAt = DateTime.UtcNow
                };

                // تجميع الرواتب حسب القسم
                report.PayrollByDepartment = payrolls
                    .GroupBy(p => p.Employee.Department?.Name ?? "غير محدد")
                    .Select(g => new PayrollByDepartmentItem
                    {
                        DepartmentName = g.Key,
                        EmployeeCount = g.Count(),
                        TotalBasicSalary = g.Sum(p => p.BasicSalary),
                        TotalAllowances = g.Sum(p => p.Allowances),
                        TotalDeductions = g.Sum(p => p.Deductions + p.AdvanceDeductions),
                        TotalNetSalary = g.Sum(p => p.NetSalary)
                    })
                    .OrderByDescending(x => x.TotalNetSalary)
                    .ToList();

                report.TotalEmployees = payrolls.Count;
                report.TotalBasicSalary = payrolls.Sum(p => p.BasicSalary);
                report.TotalAllowances = payrolls.Sum(p => p.Allowances);
                report.TotalBonuses = payrolls.Sum(p => p.Bonuses);
                report.TotalDeductions = payrolls.Sum(p => p.Deductions + p.AdvanceDeductions);
                report.TotalNetSalary = payrolls.Sum(p => p.NetSalary);
                report.AverageNetSalary = payrolls.Any() ? payrolls.Average(p => p.NetSalary) : 0;

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير الرواتب");
                throw;
            }
        }

        /// <summary>
        /// تقرير الميزانية مقابل الفعلي
        /// </summary>
        public async Task<BudgetVsActualReport> GenerateBudgetVsActualReportAsync(DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            try
            {
                // سيتم تطوير هذا التقرير عند اكتمال نظام الميزانيات
                var report = new BudgetVsActualReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    DepartmentId = departmentId,
                    GeneratedAt = DateTime.UtcNow,
                    BudgetItems = new List<BudgetVsActualItem>()
                };

                // TODO: تطوير منطق مقارنة الميزانية مع الفعلي
                
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير الميزانية مقابل الفعلي");
                throw;
            }
        }

        /// <summary>
        /// تقرير أرصدة الحسابات
        /// </summary>
        public async Task<AccountBalanceReport> GenerateAccountBalanceReportAsync(DateTime asOfDate, string? accountType = null)
        {
            try
            {
                var report = new AccountBalanceReport
                {
                    AsOfDate = asOfDate,
                    AccountType = accountType,
                    GeneratedAt = DateTime.UtcNow
                };

                report.AccountBalances = await GetAccountBalancesByTypeAsync(accountType, asOfDate);
                report.TotalBalance = report.AccountBalances.Sum(a => a.Balance);

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد تقرير أرصدة الحسابات");
                throw;
            }
        }

        // ===== الدوال المساعدة =====

        private async Task<List<AccountBalanceItem>> GetAccountBalancesByTypeAsync(string? accountType, DateTime asOfDate, int? departmentId = null)
        {
            var query = _context.Accounts.Where(a => a.IsActive);

            if (!string.IsNullOrEmpty(accountType))
            {
                query = query.Where(a => a.AccountType == accountType);
            }

            var accounts = await query.ToListAsync();
            var balances = new List<AccountBalanceItem>();

            foreach (var account in accounts)
            {
                var balance = await CalculateAccountBalanceAsOfDateAsync(account.Id, asOfDate, departmentId);
                
                balances.Add(new AccountBalanceItem
                {
                    AccountId = account.Id,
                    AccountCode = account.AccountCode,
                    AccountName = account.AccountName,
                    AccountType = account.AccountType,
                    Balance = balance
                });
            }

            return balances.OrderBy(a => a.AccountCode).ToList();
        }

        private async Task<List<AccountBalanceItem>> GetAccountBalancesByTypeForPeriodAsync(string accountType, DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            // TODO: تطوير منطق حساب أرصدة الحسابات للفترة
            return await GetAccountBalancesByTypeAsync(accountType, endDate, departmentId);
        }

        private async Task<decimal> CalculateAccountBalanceAsOfDateAsync(int accountId, DateTime asOfDate, int? departmentId = null)
        {
            // TODO: تطوير منطق حساب رصيد الحساب في تاريخ محدد
            // حالياً نعيد رصيد افتراضي
            return 0;
        }

        private async Task<decimal> CalculateFundBalanceAsOfDateAsync(int fundId, DateTime asOfDate)
        {
            var fund = await _context.Funds.FindAsync(fundId);
            if (fund == null) return 0;

            var transactions = await _context.FundTransactions
                .Where(ft => ft.FundId == fundId && ft.TransactionDate.Date <= asOfDate.Date)
                .ToListAsync();

            var totalDeposits = transactions
                .Where(t => t.TransactionType == "deposit" || t.TransactionType == "transfer_in")
                .Sum(t => t.Amount);

            var totalWithdrawals = transactions
                .Where(t => t.TransactionType == "withdrawal" || t.TransactionType == "transfer_out")
                .Sum(t => t.Amount);

            return fund.OpeningBalance + totalDeposits - totalWithdrawals;
        }
    }
}
