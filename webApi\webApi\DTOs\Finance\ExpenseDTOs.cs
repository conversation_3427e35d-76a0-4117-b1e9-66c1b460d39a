using webApi.Models.Finance;

namespace webApi.DTOs.Finance
{
    #region Expense Item Type DTOs

    public class CreateExpenseItemTypeDto
    {
        public string Name { get; set; } = string.Empty;
        public string NameArabic { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Code { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int SortOrder { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public bool IsLeaf { get; set; } = true;
        public List<ExpenseFieldSchema>? DataSchema { get; set; }
        public string? ApprovalWorkflow { get; set; }
        public bool RequiresBudgetCheck { get; set; } = true;
        public int? DefaultAccountId { get; set; }
        public decimal? MaxAmount { get; set; }
        public decimal? MinAmount { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
    }

    public class UpdateExpenseItemTypeDto
    {
        public string Name { get; set; } = string.Empty;
        public string NameArabic { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public List<ExpenseFieldSchema>? DataSchema { get; set; }
        public string? ApprovalWorkflow { get; set; }
        public bool RequiresBudgetCheck { get; set; } = true;
        public int? DefaultAccountId { get; set; }
        public decimal? MaxAmount { get; set; }
        public decimal? MinAmount { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
    }

    #endregion

    #region Expense Template DTOs

    public class CreateExpenseTemplateDto
    {
        public string Name { get; set; } = string.Empty;
        public string NameArabic { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int ExpenseItemTypeId { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsDefault { get; set; } = false;
        public ExpenseTemplateConfig? TemplateConfig { get; set; }
        public List<ExpenseApprovalLevel>? ApprovalLevels { get; set; }
        public List<ExpenseCalculationRule>? CalculationRules { get; set; }
        public List<ExpenseLineTemplate>? DefaultLines { get; set; }
    }

    public class UpdateExpenseTemplateDto
    {
        public string Name { get; set; } = string.Empty;
        public string NameArabic { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public ExpenseTemplateConfig? TemplateConfig { get; set; }
        public List<ExpenseApprovalLevel>? ApprovalLevels { get; set; }
        public List<ExpenseCalculationRule>? CalculationRules { get; set; }
        public List<ExpenseLineTemplate>? DefaultLines { get; set; }
    }

    #endregion

    #region Expense DTOs

    public class CreateExpenseDto
    {
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int ExpenseItemTypeId { get; set; }
        public int? ExpenseTemplateId { get; set; }
        public int? DepartmentId { get; set; }
        public int? CostCenterId { get; set; }
        public int? TaskId { get; set; }
        public string? Currency { get; set; }
        public decimal? ExchangeRate { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string? Priority { get; set; }
        public DateTime? DueDate { get; set; }
        public int? BudgetId { get; set; }
        public int? BudgetLineId { get; set; }
        public string? PaymentMethod { get; set; }
        public Dictionary<string, object>? CustomFields { get; set; }
        public List<CreateExpenseLineDto>? ExpenseLines { get; set; }
    }

    public class UpdateExpenseDto
    {
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int? DepartmentId { get; set; }
        public int? CostCenterId { get; set; }
        public int? TaskId { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string? Priority { get; set; }
        public DateTime? DueDate { get; set; }
        public string? PaymentMethod { get; set; }
        public Dictionary<string, object>? CustomFields { get; set; }
    }

    public class CreateExpenseLineDto
    {
        public string Description { get; set; } = string.Empty;
        public int AccountId { get; set; }
        public decimal Amount { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? UnitPrice { get; set; }
        public string? Unit { get; set; }
        public decimal? TaxRate { get; set; }
        public decimal? DiscountRate { get; set; }
        public int? CostCenterId { get; set; }
        public int? TaskId { get; set; }
        public int? DepartmentId { get; set; }
        public string? Reference { get; set; }
        public DateTime? LineDate { get; set; }
        public int SortOrder { get; set; } = 0;
    }

    public class UpdateExpenseLineDto
    {
        public string Description { get; set; } = string.Empty;
        public int AccountId { get; set; }
        public decimal Amount { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? UnitPrice { get; set; }
        public string? Unit { get; set; }
        public decimal? TaxRate { get; set; }
        public decimal? DiscountRate { get; set; }
        public int? CostCenterId { get; set; }
        public int? TaskId { get; set; }
        public int? DepartmentId { get; set; }
        public string? Reference { get; set; }
        public DateTime? LineDate { get; set; }
        public int SortOrder { get; set; } = 0;
    }

    public class ExpenseFilterDto
    {
        public string? Status { get; set; }
        public int? RequestedBy { get; set; }
        public int? DepartmentId { get; set; }
        public int? ExpenseItemTypeId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string? Priority { get; set; }
        public int? ProjectId { get; set; }
        public int? CostCenterId { get; set; }
        public string? PaymentMethod { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; }
        public string? SortDirection { get; set; } = "desc";
    }

    #endregion

    #region Response DTOs

    public class ExpenseItemTypeResponseDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameArabic { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Code { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int Level { get; set; }
        public int SortOrder { get; set; }
        public bool IsActive { get; set; }
        public bool IsLeaf { get; set; }
        public string? ApprovalWorkflow { get; set; }
        public bool RequiresBudgetCheck { get; set; }
        public int? DefaultAccountId { get; set; }
        public decimal? MaxAmount { get; set; }
        public decimal? MinAmount { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string FullPath { get; set; } = string.Empty;
        public bool HasChildren { get; set; }
        public bool CanHaveExpenses { get; set; }
        public List<ExpenseFieldSchema>? DataSchema { get; set; }
        public List<ExpenseItemTypeResponseDto>? Children { get; set; }
        public string? DefaultAccountName { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? CreatorName { get; set; }
    }

    public class ExpenseTemplateResponseDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameArabic { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int ExpenseItemTypeId { get; set; }
        public string? ExpenseItemTypeName { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public ExpenseTemplateConfig? TemplateConfig { get; set; }
        public List<ExpenseApprovalLevel>? ApprovalLevels { get; set; }
        public List<ExpenseCalculationRule>? CalculationRules { get; set; }
        public List<ExpenseLineTemplate>? DefaultLines { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? CreatorName { get; set; }
    }

    public class ExpenseResponseDto
    {
        public int Id { get; set; }
        public string ExpenseNumber { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int ExpenseItemTypeId { get; set; }
        public string? ExpenseItemTypeName { get; set; }
        public int? ExpenseTemplateId { get; set; }
        public string? ExpenseTemplateName { get; set; }
        public int RequestedBy { get; set; }
        public string? RequesterName { get; set; }
        public int? DepartmentId { get; set; }
        public string? DepartmentName { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime ExpenseDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public string? PaymentMethod { get; set; }
        public DateTime? PaymentDate { get; set; }
        public decimal? PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public bool IsEditable { get; set; }
        public bool CanBeSubmitted { get; set; }
        public bool CanBeApproved { get; set; }
        public bool CanBeRejected { get; set; }
        public bool CanBePaid { get; set; }
        public bool IsPaid { get; set; }
        public Dictionary<string, object>? CustomFields { get; set; }
        public List<ExpenseLineResponseDto>? ExpenseLines { get; set; }
        public List<ExpenseApprovalResponseDto>? ExpenseApprovals { get; set; }
        public List<ExpenseAttachmentResponseDto>? ExpenseAttachments { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? CreatorName { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string? ApproverName { get; set; }
        public DateTime? RejectedAt { get; set; }
        public string? RejectorName { get; set; }
        public string? RejectionReason { get; set; }
    }

    public class ExpenseLineResponseDto
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public int AccountId { get; set; }
        public string? AccountName { get; set; }
        public decimal Amount { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? UnitPrice { get; set; }
        public string? Unit { get; set; }
        public decimal? TaxRate { get; set; }
        public decimal? TaxAmount { get; set; }
        public decimal? DiscountRate { get; set; }
        public decimal? DiscountAmount { get; set; }
        public decimal NetAmount { get; set; }
        public decimal CalculatedAmount { get; set; }
        public string? Reference { get; set; }
        public DateTime? LineDate { get; set; }
        public int SortOrder { get; set; }
    }

    public class ExpenseApprovalResponseDto
    {
        public int Id { get; set; }
        public int Level { get; set; }
        public string LevelName { get; set; } = string.Empty;
        public int ApproverId { get; set; }
        public string? ApproverName { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Comments { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public DateTime? RejectedAt { get; set; }
        public bool IsPending { get; set; }
        public bool IsApproved { get; set; }
        public bool IsRejected { get; set; }
    }

    public class ExpenseAttachmentResponseDto
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FileSizeFormatted { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime UploadedAt { get; set; }
        public string? UploaderName { get; set; }
    }

    #endregion

    #region Summary DTOs

    public class ExpenseSummaryDto
    {
        public int TotalExpenses { get; set; }
        public decimal TotalAmount { get; set; }
        public int DraftExpenses { get; set; }
        public int SubmittedExpenses { get; set; }
        public int ApprovedExpenses { get; set; }
        public int RejectedExpenses { get; set; }
        public int PaidExpenses { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal ApprovedAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public List<ExpenseByTypeDto>? ExpensesByType { get; set; }
        public List<ExpenseByDepartmentDto>? ExpensesByDepartment { get; set; }
        public List<ExpenseByMonthDto>? ExpensesByMonth { get; set; }
    }

    public class ExpenseByTypeDto
    {
        public int ExpenseItemTypeId { get; set; }
        public string? ExpenseItemTypeName { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class ExpenseByDepartmentDto
    {
        public int? DepartmentId { get; set; }
        public string? DepartmentName { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class ExpenseByMonthDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
    }

    #endregion
}
