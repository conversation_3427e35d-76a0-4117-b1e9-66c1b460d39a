using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج التحويل التمويلي
    /// Funding Transfer Model
    /// </summary>
    [Table("funding_transfers")]
    public class FundingTransfer
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("source_fund_id")]
        public int SourceFundId { get; set; }

        [Required]
        [Column("target_account_id")]
        public int TargetAccountId { get; set; }

        [Required]
        [Column("amount", TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [Column("transfer_date")]
        public DateTime TransferDate { get; set; }

        [Required]
        [StringLength(100)]
        [Column("reference_number")]
        public string ReferenceNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Column("status")]
        public string Status { get; set; } = "pending"; // pending, completed, cancelled

        [StringLength(50)]
        [Column("transfer_type")]
        public string TransferType { get; set; } = "funding"; // funding, advance, salary

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Column("notes")]
        public string? Notes { get; set; }

        [Column("journal_entry_id")]
        public int? JournalEntryId { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        [Column("approved_by")]
        public int? ApprovedBy { get; set; }

        [Column("approved_at")]
        public long? ApprovedAt { get; set; }

        [Column("completed_at")]
        public long? CompletedAt { get; set; }

        // Navigation Properties
        [ForeignKey("SourceFundId")]
        public virtual Fund SourceFund { get; set; } = null!;

        [ForeignKey("TargetAccountId")]
        public virtual PersonalAccount TargetAccount { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        // Calculated Properties
        [NotMapped]
        public bool IsPending => Status == "pending";

        [NotMapped]
        public bool IsCompleted => Status == "completed";

        [NotMapped]
        public bool IsCancelled => Status == "cancelled";

        [NotMapped]
        public string StatusText => Status switch
        {
            "pending" => "في الانتظار",
            "completed" => "مكتمل",
            "cancelled" => "ملغي",
            _ => "غير محدد"
        };

        [NotMapped]
        public string TransferTypeText => TransferType switch
        {
            "funding" => "تمويل",
            "advance" => "سلفة",
            "salary" => "راتب",
            _ => "غير محدد"
        };
    }
}
