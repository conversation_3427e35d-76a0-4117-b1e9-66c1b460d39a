using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using webApi.Services;
using webApi.Services.Finance;
using webApi.DTOs.Finance;
using webApi.Models.Finance;

namespace webApi.Controllers
{
    /// <summary>
    /// متحكم إدارة بنود الصرف
    /// Expense Management Controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExpenseController : ControllerBase
    {
        private readonly IExpenseService _expenseService;
        private readonly IExpenseAccountingService _expenseAccountingService;
        private readonly ILogger<ExpenseController> _logger;

        public ExpenseController(
            IExpenseService expenseService,
            IExpenseAccountingService expenseAccountingService,
            ILogger<ExpenseController> logger)
        {
            _expenseService = expenseService;
            _expenseAccountingService = expenseAccountingService;
            _logger = logger;
        }

        #region Expense Item Types

        /// <summary>
        /// الحصول على جميع بنود الصرف
        /// </summary>
        [HttpGet("item-types")]
        public async Task<ActionResult<List<ExpenseItemTypeResponseDto>>> GetExpenseItemTypes()
        {
            try
            {
                var itemTypes = await _expenseService.GetExpenseItemTypesAsync();
                var response = itemTypes.Select(MapToResponseDto).ToList();
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على بنود الصرف");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على بند صرف بالمعرف
        /// </summary>
        [HttpGet("item-types/{id}")]
        public async Task<ActionResult<ExpenseItemTypeResponseDto>> GetExpenseItemType(int id)
        {
            try
            {
                var itemType = await _expenseService.GetExpenseItemTypeByIdAsync(id);
                if (itemType == null)
                    return NotFound(new { message = "بند الصرف غير موجود" });

                return Ok(MapToResponseDto(itemType));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على بند الصرف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء بند صرف جديد
        /// </summary>
        [HttpPost("item-types")]
        public async Task<ActionResult<ExpenseItemTypeResponseDto>> CreateExpenseItemType(CreateExpenseItemTypeDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var itemType = await _expenseService.CreateExpenseItemTypeAsync(dto, userId);
                var response = MapToResponseDto(itemType);
                
                return CreatedAtAction(nameof(GetExpenseItemType), new { id = itemType.Id }, response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء بند الصرف");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث بند صرف
        /// </summary>
        [HttpPut("item-types/{id}")]
        public async Task<ActionResult<ExpenseItemTypeResponseDto>> UpdateExpenseItemType(int id, UpdateExpenseItemTypeDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var itemType = await _expenseService.UpdateExpenseItemTypeAsync(id, dto, userId);
                return Ok(MapToResponseDto(itemType));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بند الصرف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف بند صرف
        /// </summary>
        [HttpDelete("item-types/{id}")]
        public async Task<ActionResult> DeleteExpenseItemType(int id)
        {
            try
            {
                var result = await _expenseService.DeleteExpenseItemTypeAsync(id);
                if (!result)
                    return NotFound(new { message = "بند الصرف غير موجود" });

                return Ok(new { message = "تم حذف بند الصرف بنجاح" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف بند الصرف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على هيكل بنود الصرف الشجري
        /// </summary>
        [HttpGet("item-types/hierarchy")]
        public async Task<ActionResult<List<ExpenseItemTypeResponseDto>>> GetExpenseItemTypeHierarchy()
        {
            try
            {
                var hierarchy = await _expenseService.GetExpenseItemTypeHierarchyAsync();
                var response = hierarchy.Select(MapToResponseDto).ToList();
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على هيكل بنود الصرف");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        #endregion

        #region Expense Templates

        /// <summary>
        /// الحصول على قوالب الصرف
        /// </summary>
        [HttpGet("templates")]
        public async Task<ActionResult<List<ExpenseTemplateResponseDto>>> GetExpenseTemplates([FromQuery] int? expenseItemTypeId = null)
        {
            try
            {
                var templates = await _expenseService.GetExpenseTemplatesAsync(expenseItemTypeId);
                var response = templates.Select(MapToResponseDto).ToList();
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قوالب الصرف");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على قالب صرف بالمعرف
        /// </summary>
        [HttpGet("templates/{id}")]
        public async Task<ActionResult<ExpenseTemplateResponseDto>> GetExpenseTemplate(int id)
        {
            try
            {
                var template = await _expenseService.GetExpenseTemplateByIdAsync(id);
                if (template == null)
                    return NotFound(new { message = "قالب الصرف غير موجود" });

                return Ok(MapToResponseDto(template));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قالب الصرف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء قالب صرف جديد
        /// </summary>
        [HttpPost("templates")]
        public async Task<ActionResult<ExpenseTemplateResponseDto>> CreateExpenseTemplate(CreateExpenseTemplateDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var template = await _expenseService.CreateExpenseTemplateAsync(dto, userId);
                var response = MapToResponseDto(template);
                
                return CreatedAtAction(nameof(GetExpenseTemplate), new { id = template.Id }, response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قالب الصرف");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث قالب صرف
        /// </summary>
        [HttpPut("templates/{id}")]
        public async Task<ActionResult<ExpenseTemplateResponseDto>> UpdateExpenseTemplate(int id, UpdateExpenseTemplateDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var template = await _expenseService.UpdateExpenseTemplateAsync(id, dto, userId);
                return Ok(MapToResponseDto(template));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث قالب الصرف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف قالب صرف
        /// </summary>
        [HttpDelete("templates/{id}")]
        public async Task<ActionResult> DeleteExpenseTemplate(int id)
        {
            try
            {
                var result = await _expenseService.DeleteExpenseTemplateAsync(id);
                if (!result)
                    return NotFound(new { message = "قالب الصرف غير موجود" });

                return Ok(new { message = "تم حذف قالب الصرف بنجاح" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف قالب الصرف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        #endregion

        #region Expenses

        /// <summary>
        /// الحصول على المصروفات مع الفلترة
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<ExpenseResponseDto>>> GetExpenses([FromQuery] ExpenseFilterDto? filter = null)
        {
            try
            {
                var expenses = await _expenseService.GetExpensesAsync(filter);
                var response = expenses.Select(MapToResponseDto).ToList();
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المصروفات");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على مصروف بالمعرف
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ExpenseResponseDto>> GetExpense(int id)
        {
            try
            {
                var expense = await _expenseService.GetExpenseByIdAsync(id);
                if (expense == null)
                    return NotFound(new { message = "المصروف غير موجود" });

                return Ok(MapToResponseDto(expense));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على المصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء مصروف جديد
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ExpenseResponseDto>> CreateExpense(CreateExpenseDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var expense = await _expenseService.CreateExpenseAsync(dto, userId);
                var response = MapToResponseDto(expense);
                
                return CreatedAtAction(nameof(GetExpense), new { id = expense.Id }, response);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المصروف");
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث مصروف
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ExpenseResponseDto>> UpdateExpense(int id, UpdateExpenseDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var expense = await _expenseService.UpdateExpenseAsync(id, dto, userId);
                return Ok(MapToResponseDto(expense));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف مصروف
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteExpense(int id)
        {
            try
            {
                var result = await _expenseService.DeleteExpenseAsync(id);
                if (!result)
                    return NotFound(new { message = "المصروف غير موجود" });

                return Ok(new { message = "تم حذف المصروف بنجاح" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// تقديم مصروف للاعتماد
        /// </summary>
        [HttpPost("{id}/submit")]
        public async Task<ActionResult<ExpenseResponseDto>> SubmitExpense(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var expense = await _expenseService.SubmitExpenseAsync(id, userId);
                return Ok(MapToResponseDto(expense));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تقديم المصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// اعتماد مصروف
        /// </summary>
        [HttpPost("{id}/approve")]
        public async Task<ActionResult<ExpenseResponseDto>> ApproveExpense(int id, [FromBody] ApproveExpenseDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var expense = await _expenseService.ApproveExpenseAsync(id, userId, dto.Comments);
                return Ok(MapToResponseDto(expense));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد المصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// رفض مصروف
        /// </summary>
        [HttpPost("{id}/reject")]
        public async Task<ActionResult<ExpenseResponseDto>> RejectExpense(int id, [FromBody] RejectExpenseDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var expense = await _expenseService.RejectExpenseAsync(id, userId, dto.Reason);
                return Ok(MapToResponseDto(expense));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفض المصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// توليد قيد محاسبي من المصروف
        /// </summary>
        [HttpPost("{id}/generate-journal-entry")]
        public async Task<ActionResult> GenerateJournalEntry(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var journalEntry = await _expenseService.GenerateJournalEntryAsync(id, userId);
                return Ok(new { 
                    message = "تم توليد القيد المحاسبي بنجاح", 
                    journalEntryId = journalEntry.Id,
                    entryNumber = journalEntry.EntryNumber
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد القيد المحاسبي للمصروف {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في الخادم", error = ex.Message });
            }
        }

        #endregion

        #region Helper Methods

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value;
            if (int.TryParse(userIdClaim, out int userId))
                return userId;
            
            throw new UnauthorizedAccessException("معرف المستخدم غير صحيح");
        }

        private static ExpenseItemTypeResponseDto MapToResponseDto(ExpenseItemType itemType)
        {
            return new ExpenseItemTypeResponseDto
            {
                Id = itemType.Id,
                Name = itemType.Name,
                NameArabic = itemType.NameArabic,
                Description = itemType.Description,
                Code = itemType.Code,
                ParentId = itemType.ParentId,
                Level = itemType.Level,
                SortOrder = itemType.SortOrder,
                IsActive = itemType.IsActive,
                IsLeaf = itemType.IsLeaf,
                ApprovalWorkflow = itemType.ApprovalWorkflow,
                RequiresBudgetCheck = itemType.RequiresBudgetCheck,
                DefaultAccountId = itemType.DefaultAccountId,
                MaxAmount = itemType.MaxAmount,
                MinAmount = itemType.MinAmount,
                Icon = itemType.Icon,
                Color = itemType.Color,
                FullPath = itemType.FullPath,
                HasChildren = itemType.HasChildren,
                CanHaveExpenses = itemType.CanHaveExpenses,
                DataSchema = itemType.ParsedDataSchema,
                Children = itemType.Children?.Select(MapToResponseDto).ToList(),
                DefaultAccountName = itemType.DefaultAccount?.AccountName,
                CreatedAt = DateTimeOffset.FromUnixTimeSeconds(itemType.CreatedAt).DateTime,
                CreatorName = itemType.Creator?.Name
            };
        }

        private static ExpenseTemplateResponseDto MapToResponseDto(ExpenseTemplate template)
        {
            return new ExpenseTemplateResponseDto
            {
                Id = template.Id,
                Name = template.Name,
                NameArabic = template.NameArabic,
                Description = template.Description,
                ExpenseItemTypeId = template.ExpenseItemTypeId,
                ExpenseItemTypeName = template.ExpenseItemType?.NameArabic,
                IsActive = template.IsActive,
                IsDefault = template.IsDefault,
                TemplateConfig = template.ParsedTemplateConfig,
                ApprovalLevels = template.ParsedApprovalLevels,
                CalculationRules = template.ParsedCalculationRules,
                DefaultLines = template.ParsedDefaultLines,
                CreatedAt = DateTimeOffset.FromUnixTimeSeconds(template.CreatedAt).DateTime,
                CreatorName = template.Creator?.Name
            };
        }

        private static ExpenseResponseDto MapToResponseDto(Expense expense)
        {
            return new ExpenseResponseDto
            {
                Id = expense.Id,
                ExpenseNumber = expense.ExpenseNumber,
                Title = expense.Title,
                Description = expense.Description,
                ExpenseItemTypeId = expense.ExpenseItemTypeId,
                ExpenseItemTypeName = expense.ExpenseItemType?.NameArabic,
                ExpenseTemplateId = expense.ExpenseTemplateId,
                ExpenseTemplateName = expense.ExpenseTemplate?.NameArabic,
                RequestedBy = expense.RequestedBy,
                RequesterName = expense.Requester?.Name,
                DepartmentId = expense.DepartmentId,
                DepartmentName = expense.Department?.Name,
                TotalAmount = expense.TotalAmount,
                Currency = expense.Currency,
                ExpenseDate = expense.ExpenseDate,
                Status = expense.Status,
                Priority = expense.Priority,
                DueDate = expense.DueDate,
                PaymentMethod = expense.PaymentMethod,
                PaymentDate = expense.PaymentDate,
                PaidAmount = expense.PaidAmount,
                RemainingAmount = expense.RemainingAmount,
                IsEditable = expense.IsEditable,
                CanBeSubmitted = expense.CanBeSubmitted,
                CanBeApproved = expense.CanBeApproved,
                CanBeRejected = expense.CanBeRejected,
                CanBePaid = expense.CanBePaid,
                IsPaid = expense.IsPaid,
                CustomFields = expense.ParsedCustomFields,
                ExpenseLines = expense.ExpenseLines?.Select(MapToResponseDto).ToList(),
                ExpenseApprovals = expense.ExpenseApprovals?.Select(MapToResponseDto).ToList(),
                ExpenseAttachments = expense.ExpenseAttachments?.Select(MapToResponseDto).ToList(),
                CreatedAt = DateTimeOffset.FromUnixTimeSeconds(expense.CreatedAt).DateTime,
                CreatorName = expense.Creator?.Name,
                SubmittedAt = expense.SubmittedAt.HasValue ? DateTimeOffset.FromUnixTimeSeconds(expense.SubmittedAt.Value).DateTime : null,
                ApprovedAt = expense.ApprovedAt.HasValue ? DateTimeOffset.FromUnixTimeSeconds(expense.ApprovedAt.Value).DateTime : null,
                ApproverName = expense.Approver?.Name,
                RejectedAt = expense.RejectedAt.HasValue ? DateTimeOffset.FromUnixTimeSeconds(expense.RejectedAt.Value).DateTime : null,
                RejectorName = expense.Rejector?.Name,
                RejectionReason = expense.RejectionReason
            };
        }

        private static ExpenseLineResponseDto MapToResponseDto(ExpenseLine line)
        {
            return new ExpenseLineResponseDto
            {
                Id = line.Id,
                Description = line.Description,
                AccountId = line.AccountId,
                AccountName = line.Account?.AccountName,
                Amount = line.Amount,
                Quantity = line.Quantity,
                UnitPrice = line.UnitPrice,
                Unit = line.Unit,
                TaxRate = line.TaxRate,
                TaxAmount = line.TaxAmount,
                DiscountRate = line.DiscountRate,
                DiscountAmount = line.DiscountAmount,
                NetAmount = line.NetAmount,
                CalculatedAmount = line.CalculatedAmount,
                Reference = line.Reference,
                LineDate = line.LineDate,
                SortOrder = line.SortOrder
            };
        }

        private static ExpenseApprovalResponseDto MapToResponseDto(ExpenseApproval approval)
        {
            return new ExpenseApprovalResponseDto
            {
                Id = approval.Id,
                Level = approval.Level,
                LevelName = approval.LevelName,
                ApproverId = approval.ApproverId,
                ApproverName = approval.Approver?.Name,
                Status = approval.Status,
                Comments = approval.Comments,
                ApprovedAt = approval.ApprovedDateTime,
                RejectedAt = approval.RejectedDateTime,
                IsPending = approval.IsPending,
                IsApproved = approval.IsApproved,
                IsRejected = approval.IsRejected
            };
        }

        private static ExpenseAttachmentResponseDto MapToResponseDto(ExpenseAttachment attachment)
        {
            return new ExpenseAttachmentResponseDto
            {
                Id = attachment.Id,
                FileName = attachment.FileName,
                OriginalName = attachment.OriginalName,
                ContentType = attachment.ContentType,
                FileSize = attachment.FileSize,
                FileSizeFormatted = attachment.FileSizeFormatted,
                Description = attachment.Description,
                UploadedAt = attachment.UploadedDateTime,
                UploaderName = attachment.Uploader?.Name
            };
        }

        #endregion

        #region Accounting Integration



        /// <summary>
        /// اعتماد المصروف مع توليد القيد المحاسبي
        /// </summary>
        [HttpPost("{id}/approve-with-accounting")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> ApproveWithAccounting(int id, [FromBody] ApproveExpenseDto dto)
        {
            try
            {
                // TODO: الحصول على معرف المستخدم الحالي من JWT
                var currentUserId = 1; // مؤقت

                var success = await _expenseAccountingService.ProcessExpenseApprovalAsync(id, currentUserId);

                if (success)
                {
                    return Ok(new { message = "تم اعتماد المصروف وتوليد القيد المحاسبي بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "فشل في اعتماد المصروف" });
                }
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد المصروف مع المحاسبة {ExpenseId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// دفع المصروف مع تحديث الصندوق
        /// </summary>
        [HttpPost("{id}/pay")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> PayExpense(int id, [FromBody] PayExpenseDto dto)
        {
            try
            {
                var success = await _expenseAccountingService.ProcessExpensePaymentAsync(id, dto.FundId, dto.PaymentMethod);

                if (success)
                {
                    return Ok(new { message = "تم دفع المصروف وتحديث الصندوق بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "فشل في دفع المصروف" });
                }
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في دفع المصروف {ExpenseId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على القيود المحاسبية للمصروف
        /// </summary>
        [HttpGet("{id}/journal-entries")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<JournalEntry>>> GetJournalEntries(int id)
        {
            try
            {
                var journalEntries = await _expenseAccountingService.GetExpenseJournalEntriesAsync(id);
                return Ok(journalEntries);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على القيود المحاسبية للمصروف {ExpenseId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// عكس القيد المحاسبي للمصروف
        /// </summary>
        [HttpPost("{id}/reverse-journal-entry")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> ReverseJournalEntry(int id, [FromBody] ReverseExpenseDto dto)
        {
            try
            {
                var success = await _expenseAccountingService.ReverseExpenseJournalEntryAsync(id, dto.Reason);

                if (success)
                {
                    return Ok(new { message = "تم عكس القيد المحاسبي بنجاح" });
                }
                else
                {
                    return BadRequest(new { message = "فشل في عكس القيد المحاسبي" });
                }
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عكس القيد المحاسبي للمصروف {ExpenseId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// حساب تأثير المصروف على الميزانية
        /// </summary>
        [HttpGet("{id}/budget-impact")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<decimal>> GetBudgetImpact(int id)
        {
            try
            {
                var impact = await _expenseAccountingService.GetExpenseBudgetImpactAsync(id);
                return Ok(new { budgetImpact = impact });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تأثير المصروف على الميزانية {ExpenseId}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        #endregion
    }

    #region Action DTOs

    public class ApproveExpenseDto
    {
        public string? Comments { get; set; }
    }

    public class RejectExpenseDto
    {
        public string Reason { get; set; } = string.Empty;
    }

    public class PayExpenseDto
    {
        public int FundId { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
    }

    public class ReverseExpenseDto
    {
        public string Reason { get; set; } = string.Empty;
    }

    #endregion
}
