using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace webApi.Models.Finance
{
    /// <summary>
    /// نموذج حركة الحساب الشخصي
    /// Personal Account Transaction Model
    /// </summary>
    [Table("personal_account_transactions")]
    public class PersonalAccountTransaction
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("account_id")]
        public int AccountId { get; set; }

        [Required]
        [StringLength(20)]
        [Column("transaction_type")]
        public string TransactionType { get; set; } = string.Empty; // debit, credit

        [Required]
        [Column("amount", TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Column("balance_after", TypeName = "decimal(18,2)")]
        public decimal BalanceAfter { get; set; }

        [StringLength(50)]
        [Column("reference_type")]
        public string? ReferenceType { get; set; } // funding, payroll, expense, adjustment

        [Column("reference_id")]
        public int? ReferenceId { get; set; }

        [StringLength(100)]
        [Column("reference_number")]
        public string? ReferenceNumber { get; set; }

        [StringLength(500)]
        [Column("description")]
        public string? Description { get; set; }

        [Column("journal_entry_id")]
        public int? JournalEntryId { get; set; }

        [Required]
        [Column("transaction_date")]
        public DateTime TransactionDate { get; set; }

        [Required]
        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Required]
        [Column("created_at")]
        public long CreatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AccountId")]
        public virtual PersonalAccount Account { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        // Calculated Properties
        [NotMapped]
        public bool IsDebit => TransactionType == "debit";

        [NotMapped]
        public bool IsCredit => TransactionType == "credit";

        [NotMapped]
        public string TransactionTypeText => TransactionType switch
        {
            "debit" => "مدين",
            "credit" => "دائن",
            _ => "غير محدد"
        };

        [NotMapped]
        public string AmountDisplay => IsDebit ? $"+{Amount:N2}" : $"-{Amount:N2}";

        [NotMapped]
        public string ReferenceTypeText => ReferenceType switch
        {
            "funding" => "تمويل",
            "payroll" => "راتب",
            "expense" => "مصروف",
            "adjustment" => "تسوية",
            _ => "غير محدد"
        };
    }
}
