import 'package:flutter/material.dart';
import 'package:flutter_application_2/services/api/api_service.dart';

import '../../models/finance/account.dart';
import '../../services/finance/accounting_service.dart';

import '../../widgets/finance/account_card.dart';
import '../../widgets/finance/create_account_dialog.dart';

/// شاشة إدارة الحسابات المحاسبية
/// Accounts Management Screen
class AccountsScreen extends StatefulWidget {
  const AccountsScreen({Key? key}) : super(key: key);

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  late AccountingService _accountingService;
  List<Account> _accounts = [];
  List<Account> _filteredAccounts = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  String _selectedAccountType = 'all';
  bool _showHierarchy = true;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _accountingService = AccountingService(context.read<ApiService>());
    _loadAccounts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final accounts = _showHierarchy 
          ? await _accountingService.getAccountsHierarchy()
          : await _accountingService.getAllAccounts();
      
      setState(() {
        _accounts = accounts;
        _filteredAccounts = accounts;
        _isLoading = false;
      });
      
      _applyFilters();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredAccounts = _accounts.where((account) {
        // فلتر البحث
        bool matchesSearch = _searchQuery.isEmpty ||
            account.accountName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            account.accountCode.toLowerCase().contains(_searchQuery.toLowerCase());

        // فلتر نوع الحساب
        bool matchesType = _selectedAccountType == 'all' ||
            account.accountType.toLowerCase() == _selectedAccountType.toLowerCase();

        return matchesSearch && matchesType;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  void _onAccountTypeChanged(String? type) {
    setState(() {
      _selectedAccountType = type ?? 'all';
    });
    _applyFilters();
  }

  void _toggleViewMode() {
    setState(() {
      _showHierarchy = !_showHierarchy;
    });
    _loadAccounts();
  }

  Future<void> _showCreateAccountDialog() async {
    final result = await showDialog<Account>(
      context: context,
      builder: (context) => CreateAccountDialog(
        accountingService: _accountingService,
        parentAccounts: _accounts.where((a) => a.parentAccountId == null).toList(),
      ),
    );

    if (result != null) {
      _loadAccounts();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء الحساب "${result.accountName}" بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الحسابات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showHierarchy ? Icons.list : Icons.account_tree),
            onPressed: _toggleViewMode,
            tooltip: _showHierarchy ? 'عرض قائمة' : 'عرض هرمي',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAccounts,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الحسابات...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: _onSearchChanged,
                ),
                const SizedBox(height: 12),
                // فلتر نوع الحساب
                Row(
                  children: [
                    const Text('نوع الحساب: ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(
                      child: DropdownButton<String>(
                        value: _selectedAccountType,
                        isExpanded: true,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                          DropdownMenuItem(value: 'asset', child: Text('أصول')),
                          DropdownMenuItem(value: 'liability', child: Text('خصوم')),
                          DropdownMenuItem(value: 'equity', child: Text('حقوق ملكية')),
                          DropdownMenuItem(value: 'revenue', child: Text('إيرادات')),
                          DropdownMenuItem(value: 'expense', child: Text('مصروفات')),
                        ],
                        onChanged: _onAccountTypeChanged,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // المحتوى الرئيسي
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateAccountDialog,
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
        tooltip: 'إضافة حساب جديد',
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل الحسابات...');
    }

    if (_error != null) {
      return ErrorDisplayWidget(
        error: _error!,
        onRetry: _loadAccounts,
      );
    }

    if (_filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _selectedAccountType != 'all'
                  ? 'لا توجد حسابات تطابق معايير البحث'
                  : 'لا توجد حسابات مسجلة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showCreateAccountDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة حساب جديد'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAccounts,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredAccounts.length,
        itemBuilder: (context, index) {
          final account = _filteredAccounts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: AccountCard(
              account: account,
              onTap: () => _showAccountDetails(account),
              showHierarchy: _showHierarchy,
            ),
          );
        },
      ),
    );
  }

  void _showAccountDetails(Account account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(account.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('رمز الحساب', account.accountCode),
            _buildDetailRow('اسم الحساب', account.accountName),
            _buildDetailRow('نوع الحساب', account.accountTypeText),
            _buildDetailRow('نوع الرصيد', account.balanceTypeText),
            _buildDetailRow('الرصيد الحالي', '${account.currentBalance.toStringAsFixed(2)} ر.س'),
            _buildDetailRow('المستوى', account.accountLevel.toString()),
            if (account.description != null)
              _buildDetailRow('الوصف', account.description!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: فتح شاشة تفاصيل الحساب
            },
            child: const Text('عرض التفاصيل'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
