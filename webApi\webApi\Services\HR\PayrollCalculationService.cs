using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.HR;
using webApi.Models.Finance;
using webApi.DTOs.HR;

namespace webApi.Services.HR
{
    /// <summary>
    /// خدمة حساب الرواتب التلقائية مع ربط بالحضور والسلف والقيود المحاسبية
    /// </summary>
    public interface IPayrollCalculationService
    {
        Task<PayrollCalculationResult> CalculateEmployeePayrollAsync(int employeeId, DateTime payPeriodStart, DateTime payPeriodEnd);
        Task<List<PayrollCalculationResult>> CalculateDepartmentPayrollAsync(int departmentId, DateTime payPeriodStart, DateTime payPeriodEnd);
        Task<List<PayrollCalculationResult>> CalculateAllEmployeesPayrollAsync(DateTime payPeriodStart, DateTime payPeriodEnd);
        Task<EmployeePayroll> ProcessPayrollAsync(PayrollCalculationResult calculation, int createdBy);
        Task<JournalEntry> GeneratePayrollJournalEntryAsync(EmployeePayroll payroll);
        Task<bool> ProcessMonthlyPayrollBatchAsync(int month, int year, int? departmentId = null);
    }

    public class PayrollCalculationService : IPayrollCalculationService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<PayrollCalculationService> _logger;
        private readonly ILoggingService _loggingService;

        public PayrollCalculationService(
            TasksDbContext context,
            ILogger<PayrollCalculationService> logger,
            ILoggingService loggingService)
        {
            _context = context;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// حساب راتب موظف واحد للفترة المحددة
        /// </summary>
        public async Task<PayrollCalculationResult> CalculateEmployeePayrollAsync(int employeeId, DateTime payPeriodStart, DateTime payPeriodEnd)
        {
            try
            {
                // الحصول على بيانات الموظف
                var employee = await _context.Users
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Id == employeeId && !u.IsDeleted);

                if (employee == null)
                    throw new ArgumentException($"الموظف غير موجود: {employeeId}");

                // التحقق من عدم وجود راتب للفترة نفسها
                var existingPayroll = await _context.EmployeePayrolls
                    .AnyAsync(p => p.EmployeeId == employeeId &&
                                  p.PayPeriodStart.Date == payPeriodStart.Date &&
                                  p.PayPeriodEnd.Date == payPeriodEnd.Date);

                if (existingPayroll)
                    throw new InvalidOperationException($"يوجد راتب مسجل للموظف {employee.FullNameArabic ?? employee.Name} للفترة من {payPeriodStart:yyyy-MM-dd} إلى {payPeriodEnd:yyyy-MM-dd}");

                var result = new PayrollCalculationResult
                {
                    EmployeeId = employeeId,
                    EmployeeName = employee.FullNameArabic ?? employee.Name,
                    EmployeeCode = employee.EmployeeId,
                    DepartmentId = employee.DepartmentId,
                    DepartmentName = employee.Department?.Name,
                    PayPeriodStart = payPeriodStart,
                    PayPeriodEnd = payPeriodEnd
                };

                // 1. حساب الراتب الأساسي
                result.BasicSalary = await CalculateBasicSalaryAsync(employee, payPeriodStart, payPeriodEnd);

                // 2. حساب البدلات
                result.Allowances = await CalculateAllowancesAsync(employee, payPeriodStart, payPeriodEnd);

                // 3. حساب المكافآت والحوافز
                result.Bonuses = await CalculateBonusesAsync(employee, payPeriodStart, payPeriodEnd);
                result.Incentives = await CalculateIncentivesAsync(employee, payPeriodStart, payPeriodEnd);

                // 4. حساب المساعدات المالية
                result.FinancialAssistance = await CalculateFinancialAssistanceAsync(employee, payPeriodStart, payPeriodEnd);

                // 5. حساب الخصومات العامة
                result.Deductions = await CalculateDeductionsAsync(employee, payPeriodStart, payPeriodEnd);

                // 6. حساب خصم السلف
                result.AdvanceDeductions = await CalculateAdvanceDeductionsAsync(employee, payPeriodStart, payPeriodEnd);

                // 7. حساب الراتب الصافي
                result.NetSalary = result.TotalEarnings - result.TotalDeductions;

                // 8. حساب إحصائيات الحضور
                result.AttendanceStats = await CalculateAttendanceStatsAsync(employee.Id, payPeriodStart, payPeriodEnd);

                _logger.LogInformation("تم حساب راتب الموظف {EmployeeName} للفترة {Period}: صافي الراتب {NetSalary:C}",
                    result.EmployeeName, $"{payPeriodStart:yyyy-MM-dd} - {payPeriodEnd:yyyy-MM-dd}", result.NetSalary);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب راتب الموظف {EmployeeId}", employeeId);
                throw;
            }
        }

        /// <summary>
        /// حساب رواتب قسم كامل
        /// </summary>
        public async Task<List<PayrollCalculationResult>> CalculateDepartmentPayrollAsync(int departmentId, DateTime payPeriodStart, DateTime payPeriodEnd)
        {
            try
            {
                var employees = await _context.Users
                    .Where(u => u.DepartmentId == departmentId && !u.IsDeleted && u.IsActive)
                    .Select(u => u.Id)
                    .ToListAsync();

                var results = new List<PayrollCalculationResult>();

                foreach (var employeeId in employees)
                {
                    try
                    {
                        var result = await CalculateEmployeePayrollAsync(employeeId, payPeriodStart, payPeriodEnd);
                        results.Add(result);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في حساب راتب الموظف {EmployeeId} في القسم {DepartmentId}", employeeId, departmentId);
                        // نواصل مع باقي الموظفين
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب رواتب القسم {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// حساب رواتب جميع الموظفين
        /// </summary>
        public async Task<List<PayrollCalculationResult>> CalculateAllEmployeesPayrollAsync(DateTime payPeriodStart, DateTime payPeriodEnd)
        {
            try
            {
                var employees = await _context.Users
                    .Where(u => !u.IsDeleted && u.IsActive)
                    .Select(u => u.Id)
                    .ToListAsync();

                var results = new List<PayrollCalculationResult>();

                foreach (var employeeId in employees)
                {
                    try
                    {
                        var result = await CalculateEmployeePayrollAsync(employeeId, payPeriodStart, payPeriodEnd);
                        results.Add(result);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في حساب راتب الموظف {EmployeeId}", employeeId);
                        // نواصل مع باقي الموظفين
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب رواتب جميع الموظفين");
                throw;
            }
        }

        /// <summary>
        /// معالجة وحفظ راتب محسوب
        /// </summary>
        public async Task<EmployeePayroll> ProcessPayrollAsync(PayrollCalculationResult calculation, int createdBy)
        {
            try
            {
                var payroll = new EmployeePayroll
                {
                    EmployeeId = calculation.EmployeeId,
                    PayPeriodStart = calculation.PayPeriodStart,
                    PayPeriodEnd = calculation.PayPeriodEnd,
                    BasicSalary = calculation.BasicSalary,
                    Allowances = calculation.Allowances,
                    Bonuses = calculation.Bonuses,
                    Incentives = calculation.Incentives,
                    Deductions = calculation.Deductions,
                    AdvanceDeductions = calculation.AdvanceDeductions,
                    FinancialAssistance = calculation.FinancialAssistance,
                    NetSalary = calculation.NetSalary,
                    PaymentStatus = "pending",
                    Notes = calculation.Notes,
                    CreatedBy = createdBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.EmployeePayrolls.Add(payroll);
                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "إنشاء راتب",
                    "EmployeePayroll",
                    payroll.Id,
                    payroll.EmployeeId,
                    $"تم إنشاء راتب للموظف {calculation.EmployeeName} للفترة {calculation.PayPeriodStart:yyyy-MM-dd} - {calculation.PayPeriodEnd:yyyy-MM-dd}. الراتب الصافي: {calculation.NetSalary:C}"
                );

                return payroll;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة راتب الموظف {EmployeeId}", calculation.EmployeeId);
                throw;
            }
        }

        /// <summary>
        /// توليد قيد محاسبي للراتب
        /// </summary>
        public async Task<JournalEntry> GeneratePayrollJournalEntryAsync(EmployeePayroll payroll)
        {
            try
            {
                // سيتم تطوير هذه الوظيفة في الخطوة التالية
                // تحتاج إلى ربط مع نظام الحسابات المحاسبية
                throw new NotImplementedException("سيتم تطوير توليد القيود المحاسبية في الخطوة التالية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد القيد المحاسبي للراتب {PayrollId}", payroll.Id);
                throw;
            }
        }

        /// <summary>
        /// معالجة رواتب شهرية مجمعة
        /// </summary>
        public async Task<bool> ProcessMonthlyPayrollBatchAsync(int month, int year, int? departmentId = null)
        {
            try
            {
                var payPeriodStart = new DateTime(year, month, 1);
                var payPeriodEnd = payPeriodStart.AddMonths(1).AddDays(-1);

                List<PayrollCalculationResult> calculations;

                if (departmentId.HasValue)
                {
                    calculations = await CalculateDepartmentPayrollAsync(departmentId.Value, payPeriodStart, payPeriodEnd);
                }
                else
                {
                    calculations = await CalculateAllEmployeesPayrollAsync(payPeriodStart, payPeriodEnd);
                }

                var successCount = 0;
                var errorCount = 0;

                foreach (var calculation in calculations)
                {
                    try
                    {
                        await ProcessPayrollAsync(calculation, 1); // TODO: استخدام معرف المستخدم الحالي
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في معالجة راتب الموظف {EmployeeName}", calculation.EmployeeName);
                        errorCount++;
                    }
                }

                _logger.LogInformation("تمت معالجة الرواتب الشهرية {Month}/{Year}: نجح {SuccessCount}، فشل {ErrorCount}",
                    month, year, successCount, errorCount);

                return errorCount == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة الرواتب الشهرية {Month}/{Year}", month, year);
                throw;
            }
        }

        // ===== الدوال المساعدة =====

        private async Task<decimal> CalculateBasicSalaryAsync(User employee, DateTime startDate, DateTime endDate)
        {
            // TODO: تطوير منطق حساب الراتب الأساسي بناءً على الحضور
            // حالياً نستخدم قيمة ثابتة أو من بيانات الموظف
            return 5000; // قيمة افتراضية - يجب ربطها ببيانات الموظف الفعلية
        }

        private async Task<decimal> CalculateAllowancesAsync(User employee, DateTime startDate, DateTime endDate)
        {
            // TODO: تطوير منطق حساب البدلات
            return 500; // قيمة افتراضية
        }

        private async Task<decimal> CalculateBonusesAsync(User employee, DateTime startDate, DateTime endDate)
        {
            // TODO: تطوير منطق حساب المكافآت
            return 0;
        }

        private async Task<decimal> CalculateIncentivesAsync(User employee, DateTime startDate, DateTime endDate)
        {
            // TODO: تطوير منطق حساب الحوافز بناءً على الأداء
            return 0;
        }

        private async Task<decimal> CalculateFinancialAssistanceAsync(User employee, DateTime startDate, DateTime endDate)
        {
            // TODO: تطوير منطق حساب المساعدات المالية
            return 0;
        }

        private async Task<decimal> CalculateDeductionsAsync(User employee, DateTime startDate, DateTime endDate)
        {
            // TODO: تطوير منطق حساب الخصومات (تأمينات، ضرائب، إلخ)
            return 200; // قيمة افتراضية
        }

        private async Task<decimal> CalculateAdvanceDeductionsAsync(User employee, DateTime startDate, DateTime endDate)
        {
            try
            {
                // حساب خصم السلف للفترة المحددة
                var advances = await _context.EmployeeAdvances
                    .Where(a => a.EmployeeId == employee.Id &&
                               a.Status == "approved" &&
                               a.RemainingAmount > 0 &&
                               a.StartDeductionDate.HasValue &&
                               a.StartDeductionDate.Value.Date <= endDate.Date)
                    .ToListAsync();

                decimal totalDeduction = 0;

                foreach (var advance in advances)
                {
                    if (advance.MonthlyDeduction.HasValue && advance.MonthlyDeduction.Value > 0)
                    {
                        var deductionAmount = Math.Min(advance.MonthlyDeduction.Value, advance.RemainingAmount);
                        totalDeduction += deductionAmount;

                        // تحديث المبلغ المتبقي (سيتم حفظه عند تأكيد الراتب)
                        // advance.RemainingAmount -= deductionAmount;
                    }
                }

                return totalDeduction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب خصم السلف للموظف {EmployeeId}", employee.Id);
                return 0;
            }
        }

        private async Task<AttendanceStats> CalculateAttendanceStatsAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            try
            {
                var attendanceRecords = await _context.EmployeeAttendances
                    .Where(a => a.EmployeeId == employeeId &&
                               a.AttendanceDate.Date >= startDate.Date &&
                               a.AttendanceDate.Date <= endDate.Date)
                    .ToListAsync();

                var stats = new AttendanceStats
                {
                    TotalDays = (endDate.Date - startDate.Date).Days + 1,
                    PresentDays = attendanceRecords.Count(a => a.Status == "present"),
                    AbsentDays = attendanceRecords.Count(a => a.Status == "absent"),
                    LateDays = attendanceRecords.Count(a => a.Status == "late"),
                    EarlyLeaveDays = attendanceRecords.Count(a => a.Status == "early_leave"),
                    HolidayDays = attendanceRecords.Count(a => a.Status == "holiday"),
                    LeaveDays = attendanceRecords.Count(a => a.Status == "on_leave"),
                    TotalWorkHours = attendanceRecords.Where(a => a.TotalHours.HasValue).Sum(a => a.TotalHours.Value),
                    OvertimeHours = attendanceRecords.Sum(a => a.OvertimeHours)
                };

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات الحضور للموظف {EmployeeId}", employeeId);
                return new AttendanceStats();
            }
        }
    }
}
