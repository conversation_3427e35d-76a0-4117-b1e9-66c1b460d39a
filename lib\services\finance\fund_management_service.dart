import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_application_2/services/api/api_service.dart';
import '../../models/finance/fund.dart';

/// خدمة إدارة الصناديق في Frontend
/// Fund Management Service for Frontend
class FundManagementService {
  final ApiService _apiService;
  static const String _baseUrl = '/api/finance/funds';

  FundManagementService(this._apiService);

  // ===================================================================
  // إدارة الصناديق
  // ===================================================================

  /// الحصول على جميع الصناديق
  Future<List<Fund>> getAllFunds() async {
    try {
      final response = await _apiService.get(_baseUrl);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => Fund.fromJson(item)).toList();
      } else {
        throw Exception('فشل في الحصول على الصناديق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على الصناديق: $e');
    }
  }

  /// الحصول على صندوق بالمعرف
  Future<Fund?> getFundById(int fundId) async {
    try {
      final response = await _apiService.get('$_baseUrl/$fundId');
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return Fund.fromJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('فشل في الحصول على الصندوق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على الصندوق: $e');
    }
  }

  /// الحصول على رصيد الصندوق
  Future<double> getFundBalance(int fundId) async {
    try {
      final response = await _apiService.get('$_baseUrl/$fundId/balance');
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return (data['balance'] ?? 0).toDouble();
      } else {
        throw Exception('فشل في الحصول على رصيد الصندوق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على رصيد الصندوق: $e');
    }
  }

  /// إنشاء صندوق جديد
  Future<Fund> createFund(CreateFundRequest request) async {
    try {
      final response = await _apiService.post(
        _baseUrl,
        body: json.encode(request.toJson()),
      );
      
      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return Fund.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في إنشاء الصندوق');
      }
    } catch (e) {
      throw Exception('خطأ في إنشاء الصندوق: $e');
    }
  }

  // ===================================================================
  // حركات الصناديق
  // ===================================================================

  /// الحصول على حركات الصندوق
  Future<List<FundTransaction>> getFundTransactions(int fundId, FundTransactionFilter filter) async {
    try {
      final queryParams = filter.toQueryParameters();
      final uri = Uri.parse('$_baseUrl/$fundId/transactions').replace(queryParameters: queryParams);
      
      final response = await _apiService.get(uri.toString());
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => FundTransaction.fromJson(item)).toList();
      } else {
        throw Exception('فشل في الحصول على حركات الصندوق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الحصول على حركات الصندوق: $e');
    }
  }

  /// إضافة حركة للصندوق
  Future<FundTransaction> addFundTransaction(int fundId, CreateFundTransactionRequest request) async {
    try {
      final response = await _apiService.post(
        '$_baseUrl/$fundId/transactions',
        body: json.encode(request.toJson()),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return FundTransaction.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في إضافة حركة الصندوق');
      }
    } catch (e) {
      throw Exception('خطأ في إضافة حركة الصندوق: $e');
    }
  }

  /// تحويل بين الصناديق
  Future<FundTransferResult> transferBetweenFunds(FundTransferRequest request) async {
    try {
      final response = await _apiService.post(
        '$_baseUrl/transfer',
        body: json.encode(request.toJson()),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return FundTransferResult.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'فشل في تحويل الأموال');
      }
    } catch (e) {
      throw Exception('خطأ في تحويل الأموال: $e');
    }
  }

  // ===================================================================
  // التقارير
  // ===================================================================

  /// تقرير حركة الصندوق
  Future<FundMovementReport> getFundMovementReport(
    int fundId, 
    DateTime fromDate, 
    DateTime toDate
  ) async {
    try {
      final queryParams = {
        'fromDate': fromDate.toIso8601String(),
        'toDate': toDate.toIso8601String(),
      };
      
      final uri = Uri.parse('$_baseUrl/$fundId/movement-report').replace(queryParameters: queryParams);
      final response = await _apiService.get(uri.toString());
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return FundMovementReport.fromJson(data);
      } else {
        throw Exception('فشل في إنتاج تقرير حركة الصندوق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إنتاج تقرير حركة الصندوق: $e');
    }
  }

  /// تقرير أرصدة الصناديق
  Future<List<FundBalanceReport>> getFundBalancesReport({DateTime? asOfDate}) async {
    try {
      String url = '$_baseUrl/balances-report';
      if (asOfDate != null) {
        url += '?asOfDate=${asOfDate.toIso8601String()}';
      }

      final response = await _apiService.get(url);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => FundBalanceReport.fromJson(item)).toList();
      } else {
        throw Exception('فشل في إنتاج تقرير أرصدة الصناديق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إنتاج تقرير أرصدة الصناديق: $e');
    }
  }
}

/// نماذج النتائج والتقارير
/// Result and Report Models

class FundTransferResult {
  final bool success;
  final String message;
  final FundTransaction? sourceTransaction;
  final FundTransaction? targetTransaction;

  FundTransferResult({
    required this.success,
    required this.message,
    this.sourceTransaction,
    this.targetTransaction,
  });

  factory FundTransferResult.fromJson(Map<String, dynamic> json) {
    return FundTransferResult(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      sourceTransaction: json['source_transaction'] != null
          ? FundTransaction.fromJson(json['source_transaction'])
          : null,
      targetTransaction: json['target_transaction'] != null
          ? FundTransaction.fromJson(json['target_transaction'])
          : null,
    );
  }
}

class FundMovementReport {
  final Fund fund;
  final DateTime fromDate;
  final DateTime toDate;
  final double openingBalance;
  final double closingBalance;
  final double totalDeposits;
  final double totalWithdrawals;
  final List<FundTransaction> transactions;

  FundMovementReport({
    required this.fund,
    required this.fromDate,
    required this.toDate,
    required this.openingBalance,
    required this.closingBalance,
    required this.totalDeposits,
    required this.totalWithdrawals,
    required this.transactions,
  });

  factory FundMovementReport.fromJson(Map<String, dynamic> json) {
    return FundMovementReport(
      fund: Fund.fromJson(json['fund']),
      fromDate: DateTime.parse(json['from_date']),
      toDate: DateTime.parse(json['to_date']),
      openingBalance: (json['opening_balance'] ?? 0).toDouble(),
      closingBalance: (json['closing_balance'] ?? 0).toDouble(),
      totalDeposits: (json['total_deposits'] ?? 0).toDouble(),
      totalWithdrawals: (json['total_withdrawals'] ?? 0).toDouble(),
      transactions: (json['transactions'] as List)
          .map((item) => FundTransaction.fromJson(item))
          .toList(),
    );
  }

  double get netMovement => totalDeposits - totalWithdrawals;
}

class FundBalanceReport {
  final int fundId;
  final String fundCode;
  final String fundName;
  final String fundType;
  final double currentBalance;
  final DateTime asOfDate;
  final bool isActive;

  FundBalanceReport({
    required this.fundId,
    required this.fundCode,
    required this.fundName,
    required this.fundType,
    required this.currentBalance,
    required this.asOfDate,
    required this.isActive,
  });

  factory FundBalanceReport.fromJson(Map<String, dynamic> json) {
    return FundBalanceReport(
      fundId: json['fund_id'] ?? 0,
      fundCode: json['fund_code'] ?? '',
      fundName: json['fund_name'] ?? '',
      fundType: json['fund_type'] ?? '',
      currentBalance: (json['current_balance'] ?? 0).toDouble(),
      asOfDate: DateTime.parse(json['as_of_date']),
      isActive: json['is_active'] ?? true,
    );
  }

  String get fundTypeText {
    switch (fundType) {
      case 'cash':
        return 'نقدي';
      case 'bank':
        return 'بنكي';
      case 'petty_cash':
        return 'نثرية';
      default:
        return 'غير محدد';
    }
  }
}
